import React, { useState } from "react";
import { BrowserRouter as Router, Routes, Route } from "react-router-dom";
import Header from "./components/Header/Header";
import LandingHeader from "./components/1stPage/LandingHeader";
import Landing from "./components/1stPage/Landing";
import Home from "./components/Home/Home";
import Chat from "./components/Chat/Chat";
import History from "./components/History/History";
import Family from "./components/Family/Family";
import Helpcenter from "./components/HelpCenter/Helpcenter";
import MealPlan from "./components/MealPlan/Mealplan";
import Login from "./components/Login/Login";
import SignUp from "./components/SignUp/SignUp";
import PrivateRoute from "./components/Protected-Route/PrivateRoute";
import AdminRoute from "./components/Protected-Route/AdminRoute";
import UserProfile from "./components/UserProfile/UserProfile";
import AdminDashboard from "./components/Admin/AdminDashboard";
import FeedbackManagement from "./components/Admin/FeedbackManagement";
import Favorites from "./components/Favorites/Favorites";
import { AdminViewProvider } from "./context/AdminViewContext";

const App = () => {
  return (
    <AdminViewProvider>
      <Router>
        <Routes>
          <Route path="/" element={<Landing/>}/>
          <Route path="/login" element={<Login/>}/>
          <Route path="/signup" element={<SignUp/>}/>
          <Route path="/home" element={
            <PrivateRoute>
              <Home />
            </PrivateRoute>}/>
          <Route path="/profile" element={
            <PrivateRoute>
              <UserProfile />
            </PrivateRoute>
          } />
          <Route path="/admin" element={
            <AdminRoute>
              <AdminDashboard />
            </AdminRoute>
          } />
          <Route path="/feedback-management" element={
            <AdminRoute>
              <FeedbackManagement />
            </AdminRoute>
          } />
          <Route path="/chat" element={<Chat/>}/>
          <Route path="/favorites" element={<Favorites/>}/>
          <Route path="/history" element={<History/>}/>
          <Route path="/family" element={<Family/>}/>
          <Route path="/help-center" element={<Helpcenter/>}/>
          <Route path="/meal-plan" element={<MealPlan/>}/>
        </Routes>
      </Router>
    </AdminViewProvider>
  );
}

export default App;
