import React, { useState } from "react";
import Header from "../Header/Header";
import Sidebar from "../Sidebar/Sidebar";
import { FaHeart, FaTimes, FaTrash } from "react-icons/fa";
import "../../../src/App.css";
import { useFavorites } from "../../components/Favorites/FavoritesContext";

const Favorites = () => {
  const [sidebarActive, setSidebarActive] = useState(false);
  const [selectedMeal, setSelectedMeal] = useState(null);
  const [showModal, setShowModal] = useState(false);
  const [notification, setNotification] = useState({ show: false, message: "" });

  const { favorites, removeFavorite } = useFavorites();


  
  const toggleSidebar = () => {
    setSidebarActive(!sidebarActive);
  };

  const handleRemoveFavorite = (e, dishId) => {
    e.stopPropagation();
    removeFavorite(dishId);
    showNotification("Removed from favorites");
  };

  const showNotification = (message) => {
    setNotification({ show: true, message });
    setTimeout(() => {
      setNotification({ show: false, message: "" });
    }, 3000);
  };

  const openMealDetails = (dish) => {
    setSelectedMeal(dish);
    setShowModal(true);
  };

  const closeMealDetails = () => {
    setShowModal(false);
  };

  return (
    <div className="app-container">
      <Header toggleSidebar={toggleSidebar} />
      <Sidebar isActive={sidebarActive} />
      <div className="content-area">
        <div className="main-content">
          <div className="container">
            <h1>MY FAVORITES</h1>
            {/* Notification */}
            {notification.show && (
              <div className="notification">{notification.message}</div>
            )}
            {favorites && favorites.length > 0 ? (
              <div className="food-grid">
                {favorites.map((dish) => (
                  <div key={dish.id || dish._id} className="food-card">
                    <div className="food-card-image">
                      <img src={dish.image} alt={dish.name} />
                      <button
                        className="favorite-btn"
                        onClick={(e) =>
                          handleRemoveFavorite(e, dish.id || dish._id)
                        }
                        title="Remove from favorites"
                      >
                        <FaTrash className="trash-icon" />
                      </button>
                    </div>
                    <div className="food-card-content">
                      <h3>{dish.name}</h3>
                      <div className="food-card-meta">
                        <div className="category-tag">
                          <span>{dish.category}</span>
                        </div>
                        <div className="rating">
                          <span>{dish.rating} &#9733;</span>
                        </div>
                      </div>
                      <div className="food-card-price">
                        <span className="price-range">
                          {dish.priceRange} Range
                        </span>
                      </div>
                      <button
                        className="view-meal-btn"
                        onClick={() => openMealDetails(dish)}
                      >
                        View Meal
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="no-favorites">
                <h2>You haven't added any favorites yet</h2>
                <p>
                  Click the heart icon on meals you like to add them to your
                  favorites
                </p>
              </div>
            )}
            {/* Meal Details Modal */}
            {showModal && selectedMeal && (
              <div className="modal-overlay">
                <div className="modal-content">
                  <div className="modal-header">
                    <h2>{selectedMeal.name}</h2>
                    <button className="close-modal" onClick={closeMealDetails}>
                      <FaTimes />
                    </button>
                  </div>
                  <div className="modal-body">
                    <div className="meal-image">
                      <img src={selectedMeal.image} alt={selectedMeal.name} />
                    </div>
                    <div className="meal-details">
                      <p className="meal-description">
                        {selectedMeal.description}
                      </p>
                      <div className="meal-meta">
                        <span className="meal-rating">
                          {selectedMeal.rating} &#9733;
                        </span>
                        <span className="meal-category">
                          {selectedMeal.category}
                        </span>
                        <span className="meal-price">
                          Calories: {selectedMeal.calories} (
                          {selectedMeal.priceRange} Range)
                        </span>
                      </div>
                      {selectedMeal.ingredients && (
                        <div className="meal-ingredients">
                          <h3>Ingredients</h3>
                          <ul>
                            {selectedMeal.ingredients.map((ingredient, idx) => (
                              <li key={idx}>{ingredient}</li>
                            ))}
                          </ul>
                        </div>
                      )}
                      {selectedMeal.steps && (
                        <div className="meal-steps">
                          <h3>Cooking Steps</h3>
                          <ol>
                            {selectedMeal.steps.map((step, idx) => (
                              <li key={idx}>{step}</li>
                            ))}
                          </ol>
                        </div>
                      )}
                      {selectedMeal.dietaryTags && (
                        <div className="meal-tags">
                          <h3>Dietary Tags</h3>
                          <div className="tags-container">
                            {selectedMeal.dietaryTags.map((tag, idx) => (
                              <span key={idx} className="dietary-tag">
                                {tag}
                              </span>
                            ))}
                          </div>
                        </div>
                      )}
                      {selectedMeal.mealType && (
                        <div className="meal-types">
                          <h3>Meal Types</h3>
                          <div className="tags-container">
                            {selectedMeal.mealType.map((type, idx) => (
                              <span key={idx} className="meal-type-tag">
                                {type}
                              </span>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Favorites;
