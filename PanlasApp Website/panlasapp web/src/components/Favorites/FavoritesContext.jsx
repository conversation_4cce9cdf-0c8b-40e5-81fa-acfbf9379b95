// $JAVASCRIPT:src/components/Favorites/FavoritesContext.js
import React, { createContext, useContext, useState, useEffect } from "react";
import axios from "axios";

const FavoritesContext = createContext();

export const useFavorites = () => useContext(FavoritesContext);

export const FavoritesProvider = ({ children }) => {
  const [favorites, setFavorites] = useState([]);
  const [loading, setLoading] = useState(true);
  const token = localStorage.getItem("token");

  useEffect(() => {
    if (!token) {
      setFavorites([]);
      setLoading(false);
      return;
    }
    setLoading(true);
    axios
      .get("http://localhost:5000/api/users/favorite-meals", {
        headers: { Authorization: `Bearer ${token}` },
      })
      .then((res) => {
        setFavorites(res.data.favoriteMeals || []);
        setLoading(false);
      })
      .catch(() => {
        setFavorites([]);
        setLoading(false);
      });
  }, [token]);

  const addFavorite = async (meal) => {
    if (!token) return;
    try {
      const res = await axios.post(
        "http://localhost:5000/api/users/favorite-meals",
        { meal }, // <-- FIXED: wrap meal in an object
        { headers: { Authorization: `Bearer ${token}` } }
      );
      setFavorites(res.data.favoriteMeals || []);
    } catch (err) {
      // Optionally handle error
    }
  };

  const removeFavorite = async (mealId) => {
    if (!token) return;
    try {
      const res = await axios.delete(
        `http://localhost:5000/api/users/favorite-meals/${mealId}`,
        { headers: { Authorization: `Bearer ${token}` } }
      );
      setFavorites(res.data.favoriteMeals || []);
    } catch (err) {
      // Optionally handle error
    }
  };

  const isFavorite = (mealId) => {
    return favorites.some((fav) => (fav._id || fav.id) === mealId);
  };

  return (
    <FavoritesContext.Provider
      value={{ favorites, addFavorite, removeFavorite, isFavorite, loading }}
    >
      {children}
    </FavoritesContext.Provider>
  );
};
