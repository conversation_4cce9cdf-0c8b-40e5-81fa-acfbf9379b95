import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { useNavigate } from 'react-router-dom';
import './AdminDashboard.css';
import SignupChart from '../SignupChart/SignupChart';
import RecentActivity from './RecentActivity';
import UserActivityLog from './UserActivityLog';
import UserRegistrationReport from './UserRegistrationReport';

function AdminDashboard() {
  const [overview, setOverview] = useState(null);
  const [signupStats, setSignupStats] = useState(null);
  const [users, setUsers] = useState([]);
  const [geolocationData, setGeolocationData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [activeTab, setActiveTab] = useState('overview');
  const navigate = useNavigate();

  // Handler functions for user management
  const handleDisableUser = async (userId, username) => {
    if (window.confirm(`Are you sure you want to disable user "${username}"? They will not be able to sign in until re-enabled.`)) {
      try {
        const token = localStorage.getItem('token');
        const response = await fetch(`http://localhost:5000/api/admin/users/${userId}/disable`, {
          method: 'PUT',
          headers: {
            'x-auth-token': token
          }
        });

        if (response.ok) {
          const responseData = await response.json();
          console.log('Disable user response:', responseData);
          // Update user in local state
          setUsers(users.map(user =>
            user._id === userId ? { ...user, isActive: false, disabledAt: new Date() } : user
          ));
          alert(`User "${username}" has been disabled successfully.`);
        } else {
          const errorData = await response.json();
          console.error('Disable user error:', errorData);
          alert(`Error disabling user: ${errorData.message}`);
        }
      } catch (error) {
        console.error('Error disabling user:', error);
        alert('Error disabling user. Please try again.');
      }
    }
  };

  const handleEnableUser = async (userId, username) => {
    if (window.confirm(`Are you sure you want to enable user "${username}"?`)) {
      try {
        const token = localStorage.getItem('token');
        const response = await fetch(`http://localhost:5000/api/admin/users/${userId}/enable`, {
          method: 'PUT',
          headers: {
            'x-auth-token': token
          }
        });

        if (response.ok) {
          const responseData = await response.json();
          console.log('Enable user response:', responseData);
          // Update user in local state
          setUsers(users.map(user =>
            user._id === userId ? { ...user, isActive: true, disabledAt: null } : user
          ));
          alert(`User "${username}" has been enabled successfully.`);
        } else {
          const errorData = await response.json();
          console.error('Enable user error:', errorData);
          alert(`Error enabling user: ${errorData.message}`);
        }
      } catch (error) {
        console.error('Error enabling user:', error);
        alert('Error enabling user. Please try again.');
      }
    }
  };

  const handleMakeAdmin = async (userId, username) => {
    if (window.confirm(`Are you sure you want to make "${username}" an admin?`)) {
      try {
        const token = localStorage.getItem('token');
        const response = await fetch(`http://localhost:5000/api/admin/users/${userId}/make-admin`, {
          method: 'PUT',
          headers: {
            'x-auth-token': token
          }
        });

        if (response.ok) {
          // Update user in local state
          setUsers(users.map(user =>
            user._id === userId ? { ...user, isAdmin: true } : user
          ));
          alert(`User "${username}" has been promoted to admin successfully.`);
        } else {
          const errorData = await response.json();
          alert(`Error making user admin: ${errorData.message}`);
        }
      } catch (error) {
        console.error('Error making user admin:', error);
        alert('Error making user admin. Please try again.');
      }
    }
  };

  const handleRemoveAdmin = async (userId, username) => {
    if (window.confirm(`Are you sure you want to remove admin privileges from "${username}"?`)) {
      try {
        const token = localStorage.getItem('token');
        const response = await fetch(`http://localhost:5000/api/admin/users/${userId}/remove-admin`, {
          method: 'PUT',
          headers: {
            'x-auth-token': token
          }
        });

        if (response.ok) {
          // Update user in local state
          setUsers(users.map(user =>
            user._id === userId ? { ...user, isAdmin: false } : user
          ));
          alert(`Admin privileges removed from "${username}" successfully.`);
        } else {
          const errorData = await response.json();
          alert(`Error removing admin privileges: ${errorData.message}`);
        }
      } catch (error) {
        console.error('Error removing admin privileges:', error);
        alert('Error removing admin privileges. Please try again.');
      }
    }
  };

  const fetchAdminData = async () => {
    try {
      setLoading(true);
      const token = localStorage.getItem('token');
      if (!token) {
        navigate('/login');
        return;
      }
      const config = {
        headers: {
          'x-auth-token': token
        }
      };

      // Fetch system overview
      const overviewRes = await axios.get('http://localhost:5000/api/admin/overview', config);
      setOverview(overviewRes.data);

      // Fetch signup statistics
      const statsRes = await axios.get('http://localhost:5000/api/admin/stats/signups', config);
      setSignupStats(statsRes.data);

      // Fetch all users
      const usersRes = await axios.get('http://localhost:5000/api/admin/users', config);
      console.log('Fetched users data:', usersRes.data);

      // Log each user's isActive status
      usersRes.data.forEach((user, index) => {
        console.log(`User ${index + 1}: ${user.username} - isActive: ${user.isActive} (type: ${typeof user.isActive})`);
      });

      setUsers(usersRes.data);

      // Fetch geolocation analytics
      const geoRes = await axios.get('http://localhost:5000/api/admin/analytics/geolocation', config);
      setGeolocationData(geoRes.data);

      setLoading(false);
    } catch (err) {
      if (err.response && err.response.status === 403) {
        setError('Access denied. Admin privileges required.');
        navigate('/home');
      } else {
        setError('Failed to load admin data. Please try again.');
        console.error('Admin dashboard error:', err);
      }
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchAdminData();
  }, []);

  if (loading) return <div className="loading">Loading admin dashboard...</div>;
  if (error) return <div className="error">{error}</div>;

  return (
    <div className="admin-dashboard">
      <h1>Admin Dashboard</h1>

      <div className="admin-tabs">
        <button
          className={`tab-button ${activeTab === 'overview' ? 'active' : ''}`}
          onClick={() => setActiveTab('overview')}
        >
          Overview
        </button>
        <button
          className={`tab-button ${activeTab === 'users' ? 'active' : ''}`}
          onClick={() => setActiveTab('users')}
        >
          Users
        </button>
        <button
          className={`tab-button ${activeTab === 'activity' ? 'active' : ''}`}
          onClick={() => setActiveTab('activity')}
        >
          Activity Log
        </button>
        <button
          className={`tab-button ${activeTab === 'reports' ? 'active' : ''}`}
          onClick={() => setActiveTab('reports')}
        >
          Reports
        </button>
        <button
          className={`tab-button ${activeTab === 'analytics' ? 'active' : ''}`}
          onClick={() => setActiveTab('analytics')}
        >
          Geolocation Analytics
        </button>
      </div>

      {activeTab === 'overview' && (
        <>
          {/* System Overview */}
          {overview && (
            <div className="dashboard-card">
              <h2>System Overview</h2>
              <div className="stats-grid">
                <div className="stat-box">
                  <h3>Total Users</h3>
                  <p className="stat-number">{overview.totalUsers}</p>
                </div>
                <div className="stat-box">
                  <h3>Total Meal Plans</h3>
                  <p className="stat-number">{overview.totalMealPlans}</p>
                </div>
                <div className="stat-box">
                  <h3>Avg. Meal Plans/User</h3>
                  <p className="stat-number">{overview.avgMealPlansPerUser}</p>
                </div>
              </div>

              <h3>Latest Signups</h3>
              <table className="admin-table">
                <thead>
                  <tr>
                    <th>Username</th>
                    <th>Email</th>
                    <th>Signup Date</th>
                  </tr>
                </thead>
                <tbody>
                  {overview.latestSignups.map((user, index) => (
                    <tr key={index}>
                      <td>{user.username}</td>
                      <td>{user.email}</td>
                      <td>{user.createdAt}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}

          {/* Recent Activity Section */}
          <RecentActivity />

          {/* Signup Statistics */}
          {signupStats && (
            <div className="dashboard-card">
              <h2>User Signup Statistics</h2>
              <div className="stats-grid">
                <div className="stat-box">
                  <h3>Today</h3>
                  <p className="stat-number">{signupStats.todaySignups}</p>
                </div>
                <div className="stat-box">
                  <h3>This Week</h3>
                  <p className="stat-number">{signupStats.weeklySignups}</p>
                </div>
                <div className="stat-box">
                  <h3>This Month</h3>
                  <p className="stat-number">{signupStats.monthlySignups}</p>
                </div>
              </div>

              <div className="chart-section">
                <SignupChart monthlyStats={signupStats.monthlyStats} />
              </div>

              <h3>Monthly Signups (Last 6 Months)</h3>
              <table className="admin-table">
                <thead>
                  <tr>
                    <th>Month</th>
                    <th>Signups</th>
                  </tr>
                </thead>
                <tbody>
                  {signupStats.monthlyStats.map((month, index) => (
                    <tr key={index}>
                      <td>{month.label}</td>
                      <td>{month.count}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </>
      )}

      {activeTab === 'users' && (
        <div className="dashboard-card">
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '20px' }}>
            <h2>All Users ({users.length})</h2>
            <button
              className="btn-refresh"
              onClick={fetchAdminData}
              style={{
                background: '#20C5AF',
                color: 'white',
                border: 'none',
                padding: '8px 16px',
                borderRadius: '4px',
                cursor: 'pointer'
              }}
            >
              Refresh Data
            </button>
          </div>
          <table className="admin-table">
            <thead>
              <tr>
                <th>Username</th>
                <th>Email</th>
                <th>Signup Date</th>
                <th>Account Age (days)</th>
                <th>Status</th>
                <th>Admin</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
              {users.map((user, index) => (
                <tr key={index} className={!user.isActive ? 'disabled-user' : ''}>
                  <td>{user.username}</td>
                  <td>{user.email}</td>
                  <td>{user.formattedCreatedAt}</td>
                  <td>{user.accountAge}</td>
                  <td>
                    <span className={`status-badge ${user.isActive ? 'active' : 'disabled'}`}>
                      {user.isActive ? 'Active' : 'Disabled'}
                    </span>
                  </td>
                  <td>{user.isAdmin ? 'Yes' : 'No'}</td>
                  <td>
                    <div className="action-buttons">
                      {!user.isAdmin ? (
                        <button
                          className="btn-make-admin"
                          onClick={() => handleMakeAdmin(user._id, user.username)}
                          title="Make Admin"
                          disabled={!user.isActive}
                        >
                          Make Admin
                        </button>
                      ) : (
                        <button
                          className="btn-remove-admin"
                          onClick={() => handleRemoveAdmin(user._id, user.username)}
                          title="Remove Admin"
                        >
                          Remove Admin
                        </button>
                      )}
                      {user.isActive ? (
                        <button
                          className="btn-disable"
                          onClick={() => handleDisableUser(user._id, user.username)}
                          title="Disable User"
                        >
                          Disable
                        </button>
                      ) : (
                        <button
                          className="btn-enable"
                          onClick={() => handleEnableUser(user._id, user.username)}
                          title="Enable User"
                        >
                          Enable
                        </button>
                      )}
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}

      {activeTab === 'activity' && <UserActivityLog />}

      {activeTab === 'reports' && <UserRegistrationReport />}

      {activeTab === 'analytics' && geolocationData && (
        <div className="dashboard-card">
          <h2>Geolocation Analytics</h2>

          <div className="stats-grid">
            <div className="stat-box">
              <h3>Total Users</h3>
              <p className="stat-number">{geolocationData.totalUsers}</p>
            </div>
            <div className="stat-box">
              <h3>With Location Data</h3>
              <p className="stat-number">{geolocationData.usersWithBarangay}</p>
            </div>
            <div className="stat-box">
              <h3>Without Location Data</h3>
              <p className="stat-number">{geolocationData.usersWithoutBarangay}</p>
            </div>
          </div>

          <h3>User Distribution by Barangay</h3>
          <table className="admin-table">
            <thead>
              <tr>
                <th>Barangay</th>
                <th>User Count</th>
                <th>Recent Signups (30 days)</th>
              </tr>
            </thead>
            <tbody>
              {geolocationData.barangayDistribution.map((barangay, index) => {
                const recentSignups = geolocationData.recentSignupsByBarangay.find(
                  recent => recent._id === barangay._id
                );
                return (
                  <tr key={index}>
                    <td>{barangay._id}</td>
                    <td>{barangay.userCount}</td>
                    <td>{recentSignups ? recentSignups.recentSignups : 0}</td>
                  </tr>
                );
              })}
            </tbody>
          </table>
        </div>
      )}
    </div>
  );
}

export default AdminDashboard;
