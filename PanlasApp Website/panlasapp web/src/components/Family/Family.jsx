import React, { useState, useEffect } from "react";
import Layout from "../Layout/Layout";
import axios from "axios";
import "../../../src/App.css";

const Family = () => {
  const [user, setUser] = useState(null);
  const [userPrefs, setUserPrefs] = useState({
    dietaryPreferences: "",
    allergies: "",
  });
  const [userPrefsSaved, setUserPrefsSaved] = useState(false);
  const [members, setMembers] = useState([]);
  const [showForm, setShowForm] = useState(false);
  const [form, setForm] = useState({
    name: "",
    dietaryPreferences: "",
    allergies: "",
    dislikedIngredients: "",
  });

  // Fetch logged-in user info
  useEffect(() => {
    const fetchUser = async () => {
      try {
        const token = localStorage.getItem("token");
        if (!token) return;
        const res = await axios.get("http://localhost:5000/api/users/profile", {
          headers: { Authorization: `Bearer ${token}` },
        });
        setUser(res.data);
        setUserPrefs({
          dietaryPreferences: res.data.dietaryPreferences || "",
          allergies: res.data.allergies || "",
        });
      } catch (err) {
        setUser(null);
      }
    };
    fetchUser();
  }, []);

  const handleUserPrefsChange = (e) => {
    setUserPrefs({ ...userPrefs, [e.target.name]: e.target.value });
    setUserPrefsSaved(false);
  };

const handleUserPrefsSave = async (e) => {
  e.preventDefault();
  try {
    const token = localStorage.getItem("token");
    if (!token) return;
    const dietaryPreferences = {
      restrictions: userPrefs.dietaryPreferences
        .split(",")
        .map(s => s.trim())
        .filter(Boolean),
      allergies: userPrefs.allergies
        .split(",")
        .map(s => s.trim())
        .filter(Boolean),
      dislikedIngredients: userPrefs.dislikedIngredients
        ? userPrefs.dislikedIngredients.split(",").map(s => s.trim()).filter(Boolean)
        : [],
    };
    await axios.put(
      "http://localhost:5000/api/users/dietary-preferences",
      dietaryPreferences,
      { headers: { Authorization: `Bearer ${token}` } }
    );
    setUserPrefsSaved(true);
  } catch (err) {
    setUserPrefsSaved(false);
    alert("Failed to save preferences.");
  }
};

  const handleInputChange = (e) => {
    setForm({ ...form, [e.target.name]: e.target.value });
  };

const handleAddMember = async (e) => {
  e.preventDefault();
  const token = localStorage.getItem("token");
  if (!token) return;
  try {
    const dietaryPreferences = {
      restrictions: form.dietaryPreferences
        .split(",")
        .map(s => s.trim())
        .filter(Boolean),
      allergies: form.allergies
        .split(",")
        .map(s => s.trim())
        .filter(Boolean),
      dislikedIngredients: form.dislikedIngredients
        ? form.dislikedIngredients.split(",").map(s => s.trim()).filter(Boolean)
        : [],
    };
    const res = await axios.post(
      "http://localhost:5000/api/users/family-members",
      {
        name: form.name,
        dietaryPreferences,
      },
      { headers: { Authorization: `Bearer ${token}` } }
    );
    setMembers(res.data.familyMembers);
    setForm({ name: "", dietaryPreferences: "", allergies: "", dislikedIngredients: "" });
    setShowForm(false);
  } catch (err) {
    alert("Failed to add family member.");
  }
};

const handleRemoveMember = async (memberId) => {
  const token = localStorage.getItem("token");
  if (!token) return;
  try {
    const res = await axios.delete(
      `http://localhost:5000/api/users/family-members/${memberId}`,
      { headers: { Authorization: `Bearer ${token}` } }
    );
    setMembers(res.data.familyMembers);
  } catch (err) {
    alert("Failed to remove family member.");
  }
};

useEffect(() => {
  // Fetch family members from backend
  const fetchFamilyMembers = async () => {
    const token = localStorage.getItem("token");
    if (!token) return;
    try {
      const res = await axios.get(
        "http://localhost:5000/api/users/family-members",
        { headers: { Authorization: `Bearer ${token}` } }
      );
      setMembers(res.data.familyMembers);
    } catch (err) {
      setMembers([]);
    }
  };
  fetchFamilyMembers();
}, []);

  return (
    <Layout>
      <div className="main-content">
        <div className="family-container">
          <div className="family-header">
            <h1>Family</h1>
          </div>
          {user && (
            <div className="family-user">
              <strong>Logged in as:</strong> {user.firstName} {user.lastName}
            </div>
          )}

          <div className="user-prefs-section">
            <h2>Your Dietary Preferences & Allergies</h2>
<form className="family-form" onSubmit={handleUserPrefsSave}>
  <label>
    Dietary Preferences:
    <input
      type="text"
      name="dietaryPreferences"
      value={userPrefs.dietaryPreferences}
      onChange={handleUserPrefsChange}
      placeholder="e.g. vegetarian, halal"
    />
  </label>
  <label>
    Allergies:
    <input
      type="text"
      name="allergies"
      value={userPrefs.allergies}
      onChange={handleUserPrefsChange}
      placeholder="e.g. peanuts, shellfish"
    />
  </label>
  <label>
    Disliked Ingredients:
    <input
      type="text"
      name="dislikedIngredients"
      value={userPrefs.dislikedIngredients || ""}
      onChange={handleUserPrefsChange}
      placeholder="e.g. eggplant, okra"
    />
  </label>
  <button type="submit">Save Preferences</button>
  {userPrefsSaved && <span style={{ color: "green", marginLeft: "1rem" }}>Saved!</span>}
</form>
          </div>

          <button
            className="add-member-btn"
            onClick={() => setShowForm((prev) => !prev)}
            style={{ marginBottom: "1rem" }}
          >
            {showForm ? "Cancel" : "Add Member"}
          </button>

{showForm && (
  <form className="family-form" onSubmit={handleAddMember} style={{ marginBottom: "2rem" }}>
    <label>
      Name:
      <input
        type="text"
        name="name"
        value={form.name}
        onChange={handleInputChange}
        required
      />
    </label>
    <label>
      Dietary Preferences:
      <input
        type="text"
        name="dietaryPreferences"
        value={form.dietaryPreferences}
        onChange={handleInputChange}
        placeholder="e.g. vegetarian, halal"
      />
    </label>
    <label>
      Allergies:
      <input
        type="text"
        name="allergies"
        value={form.allergies}
        onChange={handleInputChange}
        placeholder="e.g. peanuts, shellfish"
      />
    </label>
    <label>
      Disliked Ingredients:
      <input
        type="text"
        name="dislikedIngredients"
        value={form.dislikedIngredients || ""}
        onChange={handleInputChange}
        placeholder="e.g. eggplant, okra"
      />
    </label>
    <button type="submit">Save Member</button>
  </form>
)}

          <h2>Family Members</h2>
          {members.length === 0 ? (
            <p>No family members added yet.</p>
          ) : (
<ul className="family-members-list">
  {members.map((member, idx) => (
    <li key={member._id || idx}>
      <strong>{member.name}</strong>
      <div>
        <em>Dietary Preferences:</em>{" "}
        {member.dietaryPreferences?.restrictions?.length
          ? member.dietaryPreferences.restrictions.join(", ")
          : "None"}
      </div>
      <div>
        <em>Allergies:</em>{" "}
        {member.dietaryPreferences?.allergies?.length
          ? member.dietaryPreferences.allergies.join(", ")
          : "None"}
      </div>
      <div>
        <em>Disliked Ingredients:</em>{" "}
        {member.dietaryPreferences?.dislikedIngredients?.length
          ? member.dietaryPreferences.dislikedIngredients.join(", ")
          : "None"}
      </div>
<button
  style={{ marginTop: "0.5rem" }}
  onClick={() => handleRemoveMember(member._id)}
>
  Remove
</button>
    </li>
  ))}
</ul>
          )}
        </div>
      </div>
    </Layout>
  );
};

export default Family;
