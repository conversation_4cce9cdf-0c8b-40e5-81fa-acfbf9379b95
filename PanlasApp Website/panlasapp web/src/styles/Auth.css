/* Modern Authentication UI with Enhanced Design */

/* Container */
.auth-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background: linear-gradient(135deg, #70e4c4, #77e3c3);
  background-size: 400% 400%;
  animation: gradientShift 15s ease infinite;
  padding: 20px;
  font-family: 'Roboto', 'Segoe UI', <PERSON><PERSON>, sans-serif;
}

@keyframes gradientShift {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

/* Auth Box */
.auth-box {
  background-color: white;
  border-radius: 16px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15), 
              0 1px 5px rgba(0, 0, 0, 0.1);
  width: 100%;
  max-width: 450px;
  padding: 40px;
  transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
  position: relative;
  overflow: hidden;
}

.auth-box::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 5px;
  background: linear-gradient(to right, #70e4c4, #2bdfac);
}

.auth-box:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2), 
              0 2px 10px rgba(0, 0, 0, 0.1);
}

/* Heading */
.heading {
  font-size: 32px;
  font-weight: 700;
  color: #2c3e50;
  margin-bottom: 35px;
  text-align: center;
  position: relative;
}

.heading:after {
  content: '';
  position: absolute;
  bottom: -12px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 4px;
  background: linear-gradient(to right, #70e4c4, #70e4c4);
  border-radius: 2px;
}

/* Form */
.form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

/* Input Group */
.input-group {
  position: relative;
}

.input-group label {
  display: block;
  margin-bottom: 8px;
  font-size: 14px;
  font-weight: 500;
  color: #546e7a;
  transition: color 0.3s;
}

.input-group:focus-within label {
  color: #6e8efb;
}

.input {
  width: 100%;
  padding: 14px 18px;
  border: 2px solid #e0e0e0;
  border-radius: 8px;
  font-size: 16px;
  transition: all 0.3s;
  background-color: #f9f9f9;
}

.input:hover:not(:focus):not(.error) {
  border-color: #bdbdbd;
}

.input:focus {
  border-color: #70e4c4;
  box-shadow: 0 0 0 3px rgba(110, 142, 251, 0.2);
  outline: none;
  background-color: #fff;
}

.input.error {
  border-color: #ff3860;
  background-color: #fff0f3;
}

/* Error Message */
.error-message {
  background-color: #ffebee;
  color: #d32f2f;
  padding: 12px 16px;
  border-radius: 8px;
  margin-bottom: 20px;
  font-size: 14px;
  border-left: 4px solid #f44336;
  display: flex;
  align-items: center;
  box-shadow: 0 2px 10px rgba(244, 67, 54, 0.1);
}

.error-message::before {
  content: "⚠️";
  margin-right: 10px;
  font-size: 16px;
}

.validation-message {
  font-size: 12px;
  margin-top: 6px;
  color: #ff3860;
  display: flex;
  align-items: center;
}

.validation-message::before {
  content: "⚠";
  margin-right: 5px;
}

/* Auth Button */
.auth-button {
  background: linear-gradient(to right, #70e4c4, #2bdfac);
  color: white;
  border: none;
  padding: 14px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  margin-top: 15px;
  letter-spacing: 0.5px;
  position: relative;
  overflow: hidden;
  box-shadow: 0 4px 15px rgba(110, 142, 251, 0.3);
}

.auth-button::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.2);
  transform: translateX(-100%);
  transition: transform 0.6s;
}

.auth-button:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 20px rgba(110, 142, 251, 0.4);
}

.auth-button:hover::after {
  transform: translateX(0);
}

.auth-button:active {
  transform: translateY(0);
  box-shadow: 0 2px 10px rgba(110, 142, 251, 0.3);
}

.auth-button:disabled {
  background: linear-gradient(to right, #d1d1d1, #c8c8c8);
  cursor: not-allowed;
  box-shadow: none;
  transform: none;
}

/* Auth Link */
.auth-link {
  margin-top: 30px;
  text-align: center;
  font-size: 15px;
  color: #78909c;
  position: relative;
  padding-top: 20px;
}

.auth-link::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 1px;
  background: linear-gradient(to right, transparent, rgba(189, 189, 189, 0.5), transparent);
}

.auth-link a {
  color: #2bdfac;
  text-decoration: none;
  font-weight: 600;
  transition: all 0.3s;
  position: relative;
}

.auth-link a::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 100%;
  height: 2px;
  background-color: #2bdfac;
  transform: scaleX(0);
  transform-origin: right;
  transition: transform 0.3s ease;
}

.auth-link a:hover {
  color: #2bdfac;
}

.auth-link a:hover::after {
  transform: scaleX(1);
  transform-origin: left;
}

/* Forgot Password */
.forgot-password {
  text-align: right;
  margin-bottom: 8px;
}

.forgot-password a {
  color: #78909c;
  text-decoration: none;
  font-size: 14px;
  transition: all 0.3s;
  position: relative;
}

.forgot-password a::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 100%;
  height: 1px;
  background-color: #78909c;
  transform: scaleX(0);
  transform-origin: right;
  transition: transform 0.3s ease;
}

.forgot-password a:hover {
  color: #6e8efb;
}

.forgot-password a:hover::after {
  transform: scaleX(1);
  transform-origin: left;
}

/* Password Strength Indicator */
.password-strength-container {
  margin-top: -5px;
  margin-bottom: 10px;
}

.password-strength-bar {
  height: 6px;
  border-radius: 10px;
  margin-bottom: 8px;
  transition: all 0.4s;
  background-color: #e0e0e0;
  overflow: hidden;
  position: relative;
}

.password-strength-bar::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  border-radius: 10px;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.strength-weak .password-strength-bar::before {
  background-color: #ff3860;
  width: 30%;
}

.strength-moderate .password-strength-bar::before {
  background-color: #ffdd57;
  width: 60%;
}

.strength-strong .password-strength-bar::before {
  background-color: #23d160;
  width: 100%;
}

.password-strength {
  font-size: 12px;
  display: flex;
  justify-content: space-between;
  font-weight: 500;
}

.text-weak {
  color: #ff3860;
}

.text-moderate {
  color: #ff9800;
}

.text-strong {
  color: #23d160;
}

/* Form Grid for Signup Form */
.form-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.form-grid .full-width {
  grid-column: span 2;
}

/* Input with Icons */
.input-with-icon {
  position: relative;
}

.input-with-icon .input {
  padding-left: 45px;
}

.input-with-icon .icon {
  position: absolute;
  left: 15px;
  top: 50%;
  transform: translateY(-50%);
  color: #9e9e9e;
  transition: color 0.3s;
}

.input-with-icon:focus-within .icon {
  color: #6e8efb;
}

/* Social Login Buttons */
.social-login {
  margin-top: 25px;
  margin-bottom: 10px;
  position: relative;
  text-align: center;
}

.social-login::before {
  content: 'or';
  display: inline-block;
  position: absolute;
  top: -10px;
  left: 50%;
  transform: translateX(-50%);
  background-color: white;
  padding: 0 15px;
  color: #9e9e9e;
  font-size: 14px;
}

.social-login::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 1px;
  background: linear-gradient(to right, transparent, rgba(189, 189, 189, 0.5), transparent);
}

.social-buttons {
  display: flex;
  justify-content: center;
  gap: 15px;
  margin-top: 25px;
}

.social-button {
  width: 45px;
  height: 45px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  transition: all 0.3s;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  cursor: pointer;
}

.social-button:hover {
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
}

.social-button.google {
  background-color: white;
  color: #DB4437;
}

.social-button.facebook {
  background-color: #4267B2;
  color: white;
}

.social-button.apple {
  background-color: #000;
  color: white;
}

/* Animation for Form Transition */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.form {
  animation: fadeIn 0.5s ease-out;
}

/* Form Loader */
.form-loader {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px 0;
}

.loader {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(110, 142, 251, 0.2);
  border-radius: 50%;
  border-top-color: #6e8efb;
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* Responsive Styles */
@media (max-width: 600px) {
  .auth-box {
    padding: 30px 20px;
  }
  
  .form-grid {
    grid-template-columns: 1fr;
  }
  
  .form-grid .full-width {
    grid-column: span 1;
  }
  
  .heading {
    font-size: 28px;
  }
}

/* Focus Visible for Accessibility */
:focus-visible {
  outline: 3px solid rgba(110, 142, 251, 0.5);
  outline-offset: 2px;
}

/* Additional Animations for Input Focus */
.input-group {
  transition: transform 0.3s;
}

.input-group:focus-within {
  transform: translateY(-2px);
}

/* Custom Checkbox Styling */
.checkbox-group {
  display: flex;
  align-items: center;
  margin-top: 5px;
}

.checkbox-group input[type="checkbox"] {
  position: absolute;
  opacity: 0;
}

.checkbox-group label {
  position: relative;
  padding-left: 35px;
  cursor: pointer;
  font-size: 14px;
  color: #546e7a;
}

.checkbox-group label::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  width: 20px;
  height: 20px;
  border: 2px solid #e0e0e0;
  border-radius: 4px;
  background-color: #f9f9f9;
  transition: all 0.3s;
}

.checkbox-group input[type="checkbox"]:checked + label::before {
  background-color: #6e8efb;
  border-color: #6e8efb;
}

.checkbox-group input[type="checkbox"]:checked + label::after {
  content: '✓';
  position: absolute;
  left: 5px;
  top: 1px;
  color: white;
  font-size: 14px;
}

.checkbox-group input[type="checkbox"]:focus + label::before {
  box-shadow: 0 0 0 3px rgba(110, 142, 251, 0.2);
}