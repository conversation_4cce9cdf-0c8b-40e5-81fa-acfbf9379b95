{"name": "meal-planner-backend", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "seed": "node scripts/seedDatabase.js", "migrate-meal-plans": "node scripts/migrateMealPlans.js", "check-meal-plans": "node scripts/checkMealPlans.js", "fix-meal-plans": "node scripts/fixMealPlans.js", "fix-indexes": "node scripts/fixIndexes.js"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"axios": "^1.9.0", "bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^4.21.2", "jsonwebtoken": "^9.0.2", "mongodb": "^6.16.0", "mongoose": "^8.14.2", "nodemailer": "^7.0.3"}, "devDependencies": {"nodemon": "^3.1.10"}}