const Activity = require('../models/Activity');
const User = require('../models/User');

// Log user activity
exports.logActivity = async (req, res) => {
  try {
    const { action, details } = req.body;
    const userId = req.user.id;

    // Get IP address from request
    const ipAddress = req.ip ||
                     req.connection.remoteAddress ||
                     req.socket.remoteAddress ||
                     req.connection.socket.remoteAddress;

    // Create new activity log
    const activity = new Activity({
      user: userId,
      action,
      details,
      ipAddress
    });

    await activity.save();

    res.status(201).json(activity);
  } catch (error) {
    console.error('Activity log error:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

// Get activity for a specific user (admin only)
exports.getUserActivity = async (req, res) => {
  try {
    // Check if user is admin
    if (!req.user.isAdmin) {
      return res.status(403).json({ message: 'Access denied. Admin only.' });
    }

    const userId = req.params.userId;

    // Verify user exists
    const userExists = await User.exists({ _id: userId });
    if (!userExists) {
      return res.status(404).json({ message: 'User not found' });
    }

    // Get all activity for the user
    const activities = await Activity.find({ user: userId })
      .sort({ createdAt: -1 });

    res.json(activities);
  } catch (error) {
    console.error('Get user activity error:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

// Get recent activity for all users (admin only)
exports.getRecentActivity = async (req, res) => {
  try {
    // Check if user is admin
    if (!req.user.isAdmin) {
      return res.status(403).json({ message: 'Access denied. Admin only.' });
    }

    // Get limit from query params or default to 50
    const limit = parseInt(req.query.limit) || 50;

    // Get recent activity
    const activities = await Activity.find()
      .populate('user', 'username email')
      .sort({ createdAt: -1 })
      .limit(limit);

    res.json(activities);
  } catch (error) {
    console.error('Get recent activity error:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

// Get activity log with pagination and filtering (admin only)
exports.getActivityLog = async (req, res) => {
  try {
    // Check if user is admin
    if (!req.user.isAdmin) {
      return res.status(403).json({ message: 'Access denied. Admin only.' });
    }

    // Get query parameters
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const type = req.query.type;
    const search = req.query.search;

    // Build filter object
    const filter = {};
    if (type && type !== 'all') {
      filter.action = type;
    }

    // Calculate skip value for pagination
    const skip = (page - 1) * limit;

    // Build aggregation pipeline
    const pipeline = [
      {
        $lookup: {
          from: 'users',
          localField: 'user',
          foreignField: '_id',
          as: 'userInfo'
        }
      },
      {
        $unwind: '$userInfo'
      },
      {
        $match: {
          ...filter,
          ...(search ? {
            'userInfo.username': { $regex: search, $options: 'i' }
          } : {})
        }
      },
      {
        $project: {
          action: 1,
          details: 1,
          ipAddress: 1,
          createdAt: 1,
          username: '$userInfo.username',
          email: '$userInfo.email',
          type: '$action',
          timestamp: '$createdAt',
          description: {
            $switch: {
              branches: [
                { case: { $eq: ['$action', 'login'] }, then: 'User logged in' },
                { case: { $eq: ['$action', 'logout'] }, then: 'User logged out' },
                { case: { $eq: ['$action', 'create_meal_plan'] }, then: 'Created a meal plan' },
                { case: { $eq: ['$action', 'update_meal_plan'] }, then: 'Updated a meal plan' },
                { case: { $eq: ['$action', 'delete_meal_plan'] }, then: 'Deleted a meal plan' },
                { case: { $eq: ['$action', 'update_profile'] }, then: 'Updated profile' }
              ],
              default: '$action'
            }
          }
        }
      },
      {
        $sort: { createdAt: -1 }
      }
    ];

    // Get total count for pagination
    const totalCountPipeline = [...pipeline, { $count: 'total' }];
    const totalResult = await Activity.aggregate(totalCountPipeline);
    const total = totalResult.length > 0 ? totalResult[0].total : 0;

    // Add pagination to pipeline
    pipeline.push({ $skip: skip }, { $limit: limit });

    // Execute aggregation
    const activities = await Activity.aggregate(pipeline);

    // Calculate pagination info
    const totalPages = Math.ceil(total / limit);

    res.json({
      activities,
      currentPage: page,
      totalPages,
      totalItems: total,
      hasNextPage: page < totalPages,
      hasPrevPage: page > 1
    });
  } catch (error) {
    console.error('Get activity log error:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};
