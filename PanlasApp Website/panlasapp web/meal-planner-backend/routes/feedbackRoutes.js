const express = require('express');
const router = express.Router();
const feedbackController = require('../controllers/feedbackController');
const auth = require('../middleware/auth');

console.log('=== FEEDBACK ROUTES FILE LOADED ===');

// Test route (no auth required)
router.get('/test', (req, res) => {
  console.log('=== FEEDBACK TEST ROUTE HIT ===');
  res.json({ message: 'Feedback routes are working!' });
});

// Apply auth middleware to all routes below
router.use(auth);

// User routes
router.post('/submit', (req, res, next) => {
  console.log('=== FEEDBACK SUBMIT ROUTE HIT ===');
  console.log('Method:', req.method);
  console.log('URL:', req.url);
  console.log('Body:', req.body);
  next();
}, feedbackController.submitFeedback);

// Admin routes
router.get('/all', feedbackController.getAllFeedback);
router.put('/:feedbackId/status', feedbackController.updateFeedbackStatus);
router.post('/:feedbackId/response', feedbackController.addAdminResponse);
router.delete('/:feedbackId', feedbackController.deleteFeedback);

console.log('=== FEEDBACK ROUTES SETUP COMPLETE ===');

module.exports = router;
