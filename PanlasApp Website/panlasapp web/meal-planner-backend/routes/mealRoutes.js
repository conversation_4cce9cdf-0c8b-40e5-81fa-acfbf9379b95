const express = require('express');
const router = express.Router();
const mealController = require('../controllers/mealController');
const auth = require('../middleware/auth'); // Add auth middleware

// Get all meals
router.get('/', mealController.getMeals);

// Get a single meal
router.get('/meals:id', mealController.getFilipinoDishById);

// Create a new meal
router.post('/', mealController.createMeal);

// Update a meal
router.put('/:id', mealController.updateMeal);

// Delete a meal
router.delete('/:id', mealController.deleteMeal);

// New routes for dietary preferences
router.get('/search/filters', mealController.searchMeals);
router.get('/dietary/preferences', auth, mealController.getMealsByDietaryPreferences);
router.get('/suggestions/type', mealController.getMealSuggestions);
router.get('/popular/list', mealController.getPopularMeals);

// New routes for Filipino dishes
router.get('/filipino', mealController.getFilipinoDishes);
router.get('/filipino/:id', mealController.getFilipinoDishById);

module.exports = router;
