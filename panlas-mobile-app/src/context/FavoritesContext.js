import React, { createContext, useState, useContext, useEffect } from 'react';
import { userAPI } from '../services/api';
import { useAuth } from './AuthContext';

const FavoritesContext = createContext();

export const useFavorites = () => {
  const context = useContext(FavoritesContext);
  if (!context) {
    throw new Error('useFavorites must be used within a FavoritesProvider');
  }
  return context;
};

export const FavoritesProvider = ({ children }) => {
  const [favorites, setFavorites] = useState([]);
  const [loading, setLoading] = useState(false);
  const { isAuthenticated } = useAuth();

  // Ensure favorites is always an array
  const safeFavorites = Array.isArray(favorites) ? favorites : [];

  // Load favorites when user is authenticated
  useEffect(() => {
    if (isAuthenticated) {
      loadFavorites();
    } else {
      setFavorites([]);
    }
  }, [isAuthenticated]);

  const loadFavorites = async () => {
    try {
      setLoading(true);
      const response = await userAPI.getFavoriteMeals();
      console.log('Favorites API response:', response);

      // Handle different response structures
      let favoritesData = [];
      if (response.data) {
        if (Array.isArray(response.data)) {
          favoritesData = response.data;
        } else if (response.data.favoriteMeals) {
          favoritesData = response.data.favoriteMeals;
        }
      }

      console.log('Setting favorites:', favoritesData);
      setFavorites(Array.isArray(favoritesData) ? favoritesData : []);
    } catch (error) {
      console.error('Error loading favorites:', error);
      setFavorites([]); // Set empty array on error
    } finally {
      setLoading(false);
    }
  };

  const addFavorite = async (meal) => {
    try {
      console.log('Adding favorite meal:', meal);
      const response = await userAPI.addFavoriteMeal({ meal });
      console.log('Add favorite API response:', response);

      // Update local state - either use server response or add locally
      if (response.data && response.data.favoriteMeals) {
        setFavorites(response.data.favoriteMeals);
      } else {
        setFavorites(prev => {
          const currentFavorites = Array.isArray(prev) ? prev : [];
          return [...currentFavorites, meal];
        });
      }

      console.log('Successfully added favorite');
      return { success: true };
    } catch (error) {
      console.error('Error adding favorite:', error);
      return { success: false, error: error.response?.data?.message || 'Failed to add favorite' };
    }
  };

  const removeFavorite = async (mealId) => {
    try {
      console.log('Removing favorite meal ID:', mealId);
      const response = await userAPI.removeFavoriteMeal(mealId);
      console.log('Remove favorite API response:', response);

      // Update local state - either use server response or remove locally
      if (response.data && response.data.favoriteMeals) {
        setFavorites(response.data.favoriteMeals);
      } else {
        setFavorites(prev => {
          const currentFavorites = Array.isArray(prev) ? prev : [];
          return currentFavorites.filter(meal => (meal.id || meal._id) !== mealId);
        });
      }

      console.log('Successfully removed favorite');
      return { success: true };
    } catch (error) {
      console.error('Error removing favorite:', error);
      return { success: false, error: error.response?.data?.message || 'Failed to remove favorite' };
    }
  };

  const isFavorite = (mealId) => {
    return safeFavorites.some(meal => (meal.id || meal._id) === mealId);
  };

  const value = {
    favorites: safeFavorites,
    loading,
    addFavorite,
    removeFavorite,
    isFavorite,
    loadFavorites,
  };

  return (
    <FavoritesContext.Provider value={value}>
      {children}
    </FavoritesContext.Provider>
  );
};
