import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  ScrollView,
  TouchableOpacity,
  FlatList,
  Alert,
  RefreshControl,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { mealPlansAPI, userAPI } from '../../services/api';
import { useAuth } from '../../context/AuthContext';
import { colors, fonts, spacing, borderRadius } from '../../styles/theme';
import { commonStyles } from '../../styles/commonStyles';

const MealPlansScreen = ({ navigation }) => {
  const [mealPlans, setMealPlans] = useState([]);
  const [savedPlans, setSavedPlans] = useState([]);
  const [familyMembers, setFamilyMembers] = useState([]);
  const [userDietaryPreferences, setUserDietaryPreferences] = useState({});
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [selectedWeek, setSelectedWeek] = useState(getCurrentWeek());
  const [activeTab, setActiveTab] = useState('weekly');

  const { user } = useAuth();

  useEffect(() => {
    loadMealPlans();
    loadFamilyData();
  }, [selectedWeek]);

  const loadFamilyData = async () => {
    try {
      const [familyResponse, preferencesResponse] = await Promise.all([
        userAPI.getFamilyMembers(),
        userAPI.getDietaryPreferences(),
      ]);

      // Ensure we always set an array for familyMembers
      const familyData = familyResponse?.data;
      setFamilyMembers(Array.isArray(familyData) ? familyData : []);

      // Ensure we always set an object for preferences
      setUserDietaryPreferences(preferencesResponse?.data || {});

      console.log('Family data loaded successfully:', {
        familyMembers: Array.isArray(familyData) ? familyData.length : 0,
        preferences: preferencesResponse?.data
      });
    } catch (error) {
      console.error('Error loading family data:', error);
      // Set safe defaults on error
      setFamilyMembers([]);
      setUserDietaryPreferences({});
    }
  };

  function getCurrentWeek() {
    const today = new Date();
    const startOfWeek = new Date(today.setDate(today.getDate() - today.getDay()));
    const endOfWeek = new Date(today.setDate(today.getDate() - today.getDay() + 6));
    return {
      start: startOfWeek.toISOString().split('T')[0],
      end: endOfWeek.toISOString().split('T')[0],
    };
  }

  const loadMealPlans = async () => {
    try {
      setLoading(true);

      // Use the exact same API call as the website
      const response = await mealPlansAPI.getMealPlans();
      const plansData = response.data || [];

      console.log('Raw meal plans data:', plansData);

      // Use the exact same mapping as the website
      // The website expects meal plans to have a 'meals' array with meal objects
      setMealPlans(plansData);

      // Also try to load saved plans
      try {
        const savedResponse = await mealPlansAPI.getSavedMealPlans();
        setSavedPlans(savedResponse.data || []);
      } catch (savedError) {
        console.log('No saved plans available');
        setSavedPlans([]);
      }

    } catch (error) {
      console.error('Error loading meal plans:', error);
      Alert.alert('Error', 'Failed to load meal plans');
    } finally {
      setLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await Promise.all([loadMealPlans(), loadFamilyData()]);
    setRefreshing(false);
  };

  const getPreferencesButtonText = () => {
    try {
      // Return early if data is not loaded yet
      if (!userDietaryPreferences || !Array.isArray(familyMembers)) {
        return 'Set Preferences';
      }

      const hasUserPreferences = userDietaryPreferences?.restrictions?.length > 0 ||
                                userDietaryPreferences?.allergies?.length > 0;

      // Safely check family preferences
      const hasFamilyPreferences = familyMembers.length > 0 && familyMembers.some(member =>
        member?.dietaryPreferences?.restrictions?.length > 0 ||
        member?.dietaryPreferences?.allergies?.length > 0
      );

      if (hasUserPreferences || hasFamilyPreferences) {
        return 'Update Preferences';
      }
      return 'Set Preferences';
    } catch (error) {
      console.error('Error in getPreferencesButtonText:', error);
      return 'Set Preferences';
    }
  };

  const handleSetPreferences = () => {
    // Navigate to the Family screen within the Profile stack
    // Use getParent() to access the tab navigator
    const tabNavigator = navigation.getParent();
    if (tabNavigator) {
      tabNavigator.navigate('Profile', {
        screen: 'Family'
      });
    } else {
      // Fallback: navigate directly to Profile tab first
      navigation.navigate('Profile');
    }
  };

  const navigateWeek = (direction) => {
    const currentStart = new Date(selectedWeek.start);
    const newStart = new Date(currentStart.setDate(currentStart.getDate() + (direction * 7)));
    const newEnd = new Date(newStart.getTime() + (6 * 24 * 60 * 60 * 1000));

    setSelectedWeek({
      start: newStart.toISOString().split('T')[0],
      end: newEnd.toISOString().split('T')[0],
    });
  };

  const getDaysOfWeek = () => {
    const days = [];
    const start = new Date(selectedWeek.start);

    for (let i = 0; i < 7; i++) {
      const day = new Date(start.getTime() + (i * 24 * 60 * 60 * 1000));
      days.push({
        date: day.toISOString().split('T')[0],
        dayName: day.toLocaleDateString('en-US', { weekday: 'short' }),
        dayNumber: day.getDate(),
        isToday: day.toDateString() === new Date().toDateString(),
      });
    }

    return days;
  };

  const getMealsForDate = (date) => {
    // Find all meal plans for this date
    const plansForDate = mealPlans.filter(p => {
      if (!p.date || p.date === 'null' || p.date === '') return false;

      try {
        // Handle different date formats
        let planDate;
        if (p.date.match(/^\d{4}-\d{2}-\d{2}T/)) {
          // ISO format: "2025-06-01T00:00:00.000Z"
          planDate = p.date.split('T')[0];
        } else {
          // Full date string: "Mon Jun 02 2025 05:00:00 GMT+0500 (Pakistan Standard Time)"
          const dateObj = new Date(p.date);
          if (isNaN(dateObj.getTime())) {
            console.warn(`Invalid date found in meal plan ${p._id}:`, p.date);
            return false;
          }
          // Use local date to avoid timezone issues
          const year = dateObj.getFullYear();
          const month = String(dateObj.getMonth() + 1).padStart(2, '0');
          const day = String(dateObj.getDate()).padStart(2, '0');
          planDate = `${year}-${month}-${day}`;
          console.log(`Converted date "${p.date}" to "${planDate}"`);
        }

        console.log(`Comparing plan date "${planDate}" with target date "${date}" for plan:`, p._id);
        const isMatch = planDate === date;
        console.log(`Date match result: ${isMatch}`);
        return isMatch;
      } catch (error) {
        console.warn(`Error processing date for meal plan ${p._id}:`, error);
        return false;
      }
    });

    let allMeals = [];

    plansForDate.forEach(plan => {
      // Handle meals array format (website format)
      if (plan.meals && Array.isArray(plan.meals)) {
        console.log(`Processing meals array for plan ${plan._id}:`, plan.meals);
        plan.meals.forEach(mealItem => {
          console.log('Individual meal item:', mealItem);

          // Handle different meal item structures
          if (mealItem.mealType) {
            // If meal is null but we have other meal data in the item itself
            if (!mealItem.meal && (mealItem.name || mealItem._id)) {
              // The meal data is directly in the mealItem
              allMeals.push({
                mealType: mealItem.mealType,
                meal: mealItem, // Use the mealItem itself as the meal
                completed: mealItem.completed || false
              });
            } else if (mealItem.meal) {
              // Normal case - meal is populated
              allMeals.push({
                mealType: mealItem.mealType,
                meal: mealItem.meal,
                completed: mealItem.completed || false
              });
            } else {
              // Meal is null - skip or create placeholder
              console.warn('Meal item has null meal:', mealItem);
              allMeals.push({
                mealType: mealItem.mealType,
                meal: { name: 'Unknown Meal', calories: 0 },
                completed: mealItem.completed || false
              });
            }
          } else {
            // Legacy format - treat the whole item as a meal
            allMeals.push({
              mealType: 'breakfast', // Default
              meal: mealItem,
              completed: false
            });
          }
        });
      }

      // Handle breakfast/lunch/dinner/snack arrays format (backend format)
      ['breakfast', 'lunch', 'dinner', 'snack'].forEach(mealType => {
        if (plan[mealType] && Array.isArray(plan[mealType])) {
          plan[mealType].forEach(meal => {
            allMeals.push({
              mealType: mealType,
              meal: meal,
              completed: false
            });
          });
        }
      });
    });

    console.log(`Meals for date ${date}:`, allMeals);
    return allMeals;
  };

  const renderDayCard = (day) => {
    const meals = getMealsForDate(day.date);
    const totalCalories = meals.reduce((sum, meal) => sum + (meal.meal?.calories || 0), 0);

    return (
      <View key={day.date} style={[styles.dayCard, day.isToday && styles.todayCard]}>
        <View style={styles.dayHeader}>
          <Text style={[styles.dayName, day.isToday && styles.todayText]}>
            {day.dayName}
          </Text>
          <Text style={[styles.dayNumber, day.isToday && styles.todayText]}>
            {day.dayNumber}
          </Text>
          {totalCalories > 0 && (
            <Text style={styles.caloriesText}>{totalCalories} cal</Text>
          )}
        </View>

        <View style={styles.mealsContainer}>
          {['breakfast', 'lunch', 'dinner'].map(mealType => {
            const meal = meals.find(m => m.mealType?.toLowerCase() === mealType);
            return (
              <TouchableOpacity
                key={mealType}
                style={[
                  styles.mealSlot,
                  meal && styles.mealSlotFilled
                ]}
                onPress={() => navigation.navigate('CreateMealPlan', {
                  selectedDate: day.date,
                  mealType,
                  existingMeal: meal,
                  allMealsForDate: meals, // Pass all meals for this date
                })}
              >
                <Text style={styles.mealTypeText}>
                  {mealType.charAt(0).toUpperCase() + mealType.slice(1)}
                </Text>
                {meal ? (
                  <View style={styles.mealCardContent}>
                    <Text style={styles.mealNameText} numberOfLines={1}>
                      {meal.meal?.name || meal.name || 'Unknown Meal'}
                    </Text>
                    <View style={styles.mealMetaInfo}>
                      {(meal.meal?.category || meal.category) && (
                        <View style={styles.mealCategoryTag}>
                          <Text style={styles.mealCategoryText}>
                            {(() => {
                              const category = meal.meal?.category || meal.category;
                              return Array.isArray(category) ? category[0] : category;
                            })()}
                          </Text>
                        </View>
                      )}
                      {(meal.meal?.calories || meal.calories) && (
                        <Text style={styles.mealCaloriesText}>
                          {meal.meal?.calories || meal.calories} cal
                        </Text>
                      )}
                    </View>
                    {(meal.meal?.description || meal.description) && (
                      <Text style={styles.mealDescriptionText} numberOfLines={1}>
                        {meal.meal?.description || meal.description}
                      </Text>
                    )}
                  </View>
                ) : (
                  <View style={styles.addMealButton}>
                    <Ionicons name="add" size={16} color={colors.primary} />
                    <Text style={styles.addMealText}>Add</Text>
                  </View>
                )}
              </TouchableOpacity>
            );
          })}
        </View>
      </View>
    );
  };

  const renderSavedPlan = ({ item: plan }) => (
    <TouchableOpacity style={styles.savedPlanCard}>
      <View style={styles.savedPlanHeader}>
        <Text style={styles.savedPlanName}>{plan.name}</Text>
        <TouchableOpacity style={styles.favoritePlanButton}>
          <Ionicons name="heart-outline" size={20} color={colors.textSecondary} />
        </TouchableOpacity>
      </View>
      <Text style={styles.savedPlanDate}>
        Created: {new Date(plan.createdAt).toLocaleDateString()}
      </Text>
      <Text style={styles.savedPlanDescription}>
        {plan.description || `${plan.meals?.length || 0} meals planned`}
      </Text>
      <View style={styles.savedPlanActions}>
        <TouchableOpacity style={styles.useTemplateButton}>
          <Text style={styles.useTemplateText}>Use Template</Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.favoritePlanActionButton}>
          <Ionicons name="heart-outline" size={16} color={colors.primary} />
          <Text style={styles.favoritePlanActionText}>Favorite</Text>
        </TouchableOpacity>
      </View>
    </TouchableOpacity>
  );

  const renderWeeklyView = () => (
    <ScrollView
      refreshControl={
        <RefreshControl
          refreshing={refreshing}
          onRefresh={onRefresh}
          colors={[colors.primary]}
        />
      }
    >
      {/* Week Navigation */}
      <View style={styles.weekNavigation}>
        <TouchableOpacity
          style={styles.weekNavButton}
          onPress={() => navigateWeek(-1)}
        >
          <Ionicons name="chevron-back" size={24} color={colors.primary} />
        </TouchableOpacity>

        <View style={styles.weekInfo}>
          <Text style={styles.weekText}>
            {new Date(selectedWeek.start).toLocaleDateString('en-US', {
              month: 'short',
              day: 'numeric'
            })} - {new Date(selectedWeek.end).toLocaleDateString('en-US', {
              month: 'short',
              day: 'numeric'
            })}
          </Text>
        </View>

        <TouchableOpacity
          style={styles.weekNavButton}
          onPress={() => navigateWeek(1)}
        >
          <Ionicons name="chevron-forward" size={24} color={colors.primary} />
        </TouchableOpacity>
      </View>

      {/* Days Grid */}
      <View style={styles.daysGrid}>
        {getDaysOfWeek().map(renderDayCard)}
      </View>

      {/* Quick Actions */}
      <View style={styles.quickActions}>
        <TouchableOpacity
          style={styles.quickActionButton}
          onPress={() => navigation.navigate('CreateMealPlan', {
            selectedDate: new Date().toISOString().split('T')[0],
          })}
        >
          <Ionicons name="add-circle-outline" size={24} color={colors.primary} />
          <Text style={styles.quickActionText}>Add Meal</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.quickActionButton}
          onPress={handleSetPreferences}
        >
          <Ionicons name="settings-outline" size={24} color={colors.primary} />
          <Text style={styles.quickActionText}>{getPreferencesButtonText()}</Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.quickActionButton}>
          <Ionicons name="copy-outline" size={24} color={colors.primary} />
          <Text style={styles.quickActionText}>Copy Week</Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.quickActionButton}>
          <Ionicons name="save-outline" size={24} color={colors.primary} />
          <Text style={styles.quickActionText}>Save Template</Text>
        </TouchableOpacity>
      </View>
    </ScrollView>
  );

  const renderSavedView = () => (
    <View style={styles.savedContainer}>
      <FlatList
        data={savedPlans}
        renderItem={renderSavedPlan}
        keyExtractor={(item) => item.id || item._id}
        contentContainerStyle={styles.savedList}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={[colors.primary]}
          />
        }
        ListEmptyComponent={
          <View style={commonStyles.emptyContainer}>
            <Ionicons name="bookmark-outline" size={64} color={colors.textSecondary} />
            <Text style={commonStyles.emptyTitle}>No saved meal plans</Text>
            <Text style={commonStyles.emptySubtitle}>
              Create and save meal plan templates for quick reuse
            </Text>
          </View>
        }
      />
    </View>
  );

  return (
    <SafeAreaView style={commonStyles.container}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Meal Plans</Text>
        <TouchableOpacity
          style={styles.headerButton}
          onPress={() => navigation.navigate('CreateMealPlan')}
        >
          <Ionicons name="add" size={24} color={colors.surface} />
        </TouchableOpacity>
      </View>

      {/* Tabs */}
      <View style={styles.tabsContainer}>
        <TouchableOpacity
          style={[styles.tabButton, activeTab === 'weekly' && styles.tabButtonActive]}
          onPress={() => setActiveTab('weekly')}
        >
          <Text style={[
            styles.tabButtonText,
            activeTab === 'weekly' && styles.tabButtonTextActive
          ]}>
            Weekly View
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.tabButton, activeTab === 'saved' && styles.tabButtonActive]}
          onPress={() => setActiveTab('saved')}
        >
          <Text style={[
            styles.tabButtonText,
            activeTab === 'saved' && styles.tabButtonTextActive
          ]}>
            Saved Plans
          </Text>
        </TouchableOpacity>
      </View>

      {/* Content */}
      {loading ? (
        <View style={commonStyles.loadingContainer}>
          <Text style={commonStyles.loadingText}>Loading meal plans...</Text>
        </View>
      ) : (
        <>
          {activeTab === 'weekly' && renderWeeklyView()}
          {activeTab === 'saved' && renderSavedView()}
        </>
      )}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  header: {
    backgroundColor: colors.primary,
    paddingVertical: spacing.md,
    paddingHorizontal: spacing.md,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  headerTitle: {
    fontSize: fonts.sizes.xlarge,
    fontWeight: 'bold',
    color: colors.surface,
  },
  headerButton: {
    padding: spacing.sm,
  },
  tabsContainer: {
    flexDirection: 'row',
    backgroundColor: colors.surface,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  tabButton: {
    flex: 1,
    paddingVertical: spacing.md,
    alignItems: 'center',
    borderBottomWidth: 2,
    borderBottomColor: 'transparent',
  },
  tabButtonActive: {
    borderBottomColor: colors.primary,
  },
  tabButtonText: {
    fontSize: fonts.sizes.medium,
    color: colors.textSecondary,
    fontWeight: '500',
  },
  tabButtonTextActive: {
    color: colors.primary,
  },
  weekNavigation: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.md,
    backgroundColor: colors.surface,
  },
  weekNavButton: {
    padding: spacing.sm,
  },
  weekInfo: {
    flex: 1,
    alignItems: 'center',
  },
  weekText: {
    fontSize: fonts.sizes.large,
    fontWeight: 'bold',
    color: colors.text,
  },
  daysGrid: {
    padding: spacing.md,
  },
  dayCard: {
    backgroundColor: colors.surface,
    borderRadius: borderRadius.medium,
    padding: spacing.md,
    marginBottom: spacing.md,
    ...commonStyles.shadowSmall,
  },
  todayCard: {
    borderWidth: 2,
    borderColor: colors.primary,
  },
  dayHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  dayName: {
    fontSize: fonts.sizes.medium,
    fontWeight: 'bold',
    color: colors.text,
  },
  dayNumber: {
    fontSize: fonts.sizes.large,
    fontWeight: 'bold',
    color: colors.text,
  },
  todayText: {
    color: colors.primary,
  },
  caloriesText: {
    fontSize: fonts.sizes.small,
    color: colors.textSecondary,
  },
  mealsContainer: {
    gap: spacing.sm,
  },
  mealSlot: {
    backgroundColor: colors.background,
    borderRadius: borderRadius.small,
    padding: spacing.sm,
    borderWidth: 1,
    borderColor: colors.border,
    borderStyle: 'dashed',
  },
  mealSlotFilled: {
    backgroundColor: colors.surface,
    borderStyle: 'solid',
    borderColor: colors.primary,
  },
  mealTypeText: {
    fontSize: fonts.sizes.small,
    fontWeight: '500',
    color: colors.textSecondary,
    marginBottom: spacing.xs,
  },
  mealNameText: {
    fontSize: fonts.sizes.medium,
    color: colors.text,
    fontWeight: '500',
  },
  mealCardContent: {
    flex: 1,
  },
  mealMetaInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginTop: spacing.xs,
  },
  mealCategoryTag: {
    backgroundColor: colors.primary,
    paddingHorizontal: spacing.xs,
    paddingVertical: 2,
    borderRadius: borderRadius.small,
    marginRight: spacing.xs,
  },
  mealCategoryText: {
    color: colors.surface,
    fontSize: fonts.sizes.tiny || 10,
    fontWeight: '500',
  },
  mealCaloriesText: {
    fontSize: fonts.sizes.small,
    color: colors.textSecondary,
    fontWeight: '500',
  },
  mealDescriptionText: {
    fontSize: fonts.sizes.tiny || 10,
    color: colors.textSecondary,
    marginTop: spacing.xs,
    lineHeight: 12,
  },
  addMealButton: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  addMealText: {
    fontSize: fonts.sizes.small,
    color: colors.primary,
    marginLeft: spacing.xs,
  },
  quickActions: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.lg,
    backgroundColor: colors.surface,
    marginTop: spacing.md,
  },
  quickActionButton: {
    alignItems: 'center',
    padding: spacing.md,
  },
  quickActionText: {
    fontSize: fonts.sizes.small,
    color: colors.primary,
    marginTop: spacing.xs,
    fontWeight: '500',
  },
  savedContainer: {
    flex: 1,
  },
  savedList: {
    padding: spacing.md,
  },
  savedPlanCard: {
    backgroundColor: colors.surface,
    borderRadius: borderRadius.medium,
    padding: spacing.md,
    marginBottom: spacing.md,
    ...commonStyles.shadowSmall,
  },
  savedPlanHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  savedPlanName: {
    fontSize: fonts.sizes.medium,
    fontWeight: 'bold',
    color: colors.text,
  },
  savedPlanDate: {
    fontSize: fonts.sizes.small,
    color: colors.textSecondary,
  },
  savedPlanDescription: {
    fontSize: fonts.sizes.small,
    color: colors.textSecondary,
    marginBottom: spacing.md,
  },
  savedPlanActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  useTemplateButton: {
    backgroundColor: colors.primary,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderRadius: borderRadius.small,
  },
  useTemplateText: {
    color: colors.surface,
    fontSize: fonts.sizes.small,
    fontWeight: '500',
  },
  favoritePlanButton: {
    padding: spacing.sm,
  },
  favoritePlanActionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.background,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderRadius: borderRadius.small,
    borderWidth: 1,
    borderColor: colors.primary,
  },
  favoritePlanActionText: {
    fontSize: fonts.sizes.small,
    color: colors.primary,
    marginLeft: spacing.xs,
    fontWeight: '500',
  },
});

export default MealPlansScreen;
