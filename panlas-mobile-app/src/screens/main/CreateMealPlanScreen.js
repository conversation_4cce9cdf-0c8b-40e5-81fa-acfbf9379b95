import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  ScrollView,
  TouchableOpacity,
  FlatList,
  Image,
  Alert,
  Modal,
  TextInput,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { mealsAPI, mealPlansAPI, userAPI } from '../../services/api';
import { useAuth } from '../../context/AuthContext';
import { colors, fonts, spacing, borderRadius } from '../../styles/theme';
import { commonStyles } from '../../styles/commonStyles';

const CreateMealPlanScreen = ({ navigation, route }) => {
  const { selectedDate, mealType, existingMeal, selectedMeal, allMealsForDate } = route.params || {};

  const [currentDate, setCurrentDate] = useState(selectedDate || new Date().toISOString().split('T')[0]);
  const [currentMealType, setCurrentMealType] = useState(mealType || 'breakfast');
  const [selectedMeals, setSelectedMeals] = useState({});
  const [availableMeals, setAvailableMeals] = useState([]);
  const [loading, setLoading] = useState(false);
  const [showMealSelector, setShowMealSelector] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredMeals, setFilteredMeals] = useState([]);

  const { user } = useAuth();
  const mealTypes = ['breakfast', 'lunch', 'dinner', 'snack'];

  useEffect(() => {
    loadAvailableMeals();
    // Load existing meal plan using passed data or API call
    loadExistingMealPlan();
  }, []);

  // Load existing meal plan for the selected date
  const loadExistingMealPlan = async () => {
    try {
      console.log('Loading existing meal plan for date:', currentDate);

      // Try to get all meal plans and find the one for our date
      const allMealPlansResponse = await mealPlansAPI.getMealPlans();
      console.log('All meal plans response:', allMealPlansResponse);

      let existingPlan = null;
      if (allMealPlansResponse.data && Array.isArray(allMealPlansResponse.data)) {
        // Find the meal plan for the current date
        existingPlan = allMealPlansResponse.data.find(plan => plan.date === currentDate);
        console.log('Found existing plan for date:', currentDate, existingPlan);
      }

      const existingMeals = {};

      // Initialize empty arrays for all meal types
      mealTypes.forEach(mealType => {
        existingMeals[mealType] = [];
      });

      // If we found an existing plan, extract meals for ALL meal types
      if (existingPlan) {
        mealTypes.forEach(mealType => {
          if (existingPlan[mealType] && Array.isArray(existingPlan[mealType])) {
            existingMeals[mealType] = existingPlan[mealType].map(mealItem => {
              // Handle the nested meal structure from backend
              return mealItem.meal || mealItem;
            });
          }
        });

        console.log('Extracted existing meals for ALL meal types:', existingMeals);
      } else {
        console.log('No existing meal plan found for date:', currentDate);
      }

      // Add any pre-selected meals from navigation
      if (selectedMeal) {
        existingMeals[currentMealType] = [...(existingMeals[currentMealType] || []), selectedMeal];
        console.log('Added selectedMeal to', currentMealType, selectedMeal);
      } else if (existingMeal && existingMeal.meal) {
        // Don't duplicate the existing meal if it's already in the list
        const mealData = existingMeal.meal;
        const mealExists = existingMeals[currentMealType].some(meal =>
          (meal._id || meal.id) === (mealData._id || mealData.id)
        );
        if (!mealExists) {
          existingMeals[currentMealType].push(mealData);
          console.log('Added existingMeal to', currentMealType, mealData);
        }
      }

      setSelectedMeals(existingMeals);
      console.log('Final selected meals state:', existingMeals);

    } catch (error) {
      console.error('Error loading existing meal plan:', error);
      // Initialize empty structure on error
      const emptyMeals = {};
      mealTypes.forEach(mealType => {
        emptyMeals[mealType] = [];
      });

      // Add any pre-selected meals
      if (selectedMeal) {
        emptyMeals[currentMealType] = [selectedMeal];
      } else if (existingMeal && existingMeal.meal) {
        emptyMeals[currentMealType] = [existingMeal.meal];
      }

      setSelectedMeals(emptyMeals);
    }
  };

  useEffect(() => {
    filterMeals();
  }, [availableMeals, searchQuery]);

  const loadAvailableMeals = async () => {
    try {
      setLoading(true);
      const response = await mealsAPI.getFilipinoDishes();
      setAvailableMeals(response.data || []);
    } catch (error) {
      console.error('Error loading meals:', error);
      Alert.alert('Error', 'Failed to load available meals');
    } finally {
      setLoading(false);
    }
  };

  const filterMeals = () => {
    let filtered = availableMeals;

    if (searchQuery.trim()) {
      filtered = filtered.filter(meal =>
        meal.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        (meal.description && meal.description.toLowerCase().includes(searchQuery.toLowerCase()))
      );
    }

    setFilteredMeals(filtered);
  };

  // Helper function to get valid meal ID for backend
  const getMealIdForBackend = (meal) => {
    // The backend meals array expects ObjectId references
    // Send the original meal ID as a string (should be a valid ObjectId from database)
    const mealId = meal._id || meal.id;
    const mealIdString = String(mealId);
    console.log('Meal ID being sent:', mealIdString, 'Type:', typeof mealIdString, 'Original meal:', meal.name);
    return mealIdString;
  };

  const handleMealSelect = (meal) => {
    const currentMeals = selectedMeals[currentMealType] || [];
    const isSelected = currentMeals.some(m => (m.id || m._id) === (meal.id || meal._id));

    if (isSelected) {
      // Remove meal
      setSelectedMeals(prev => ({
        ...prev,
        [currentMealType]: currentMeals.filter(m => (m.id || m._id) !== (meal.id || meal._id))
      }));
    } else {
      // Add meal
      setSelectedMeals(prev => ({
        ...prev,
        [currentMealType]: [...currentMeals, meal]
      }));
    }
  };

  const handleSaveMealPlan = async () => {
    try {
      setLoading(true);

      // Check if any meals are selected
      const hasSelectedMeals = Object.values(selectedMeals).some(meals => meals && meals.length > 0);

      if (!hasSelectedMeals) {
        Alert.alert('Error', 'Please select at least one meal');
        return;
      }

      // For each meal type, save/update the meals individually
      for (const [mealType, meals] of Object.entries(selectedMeals)) {
        if (meals && meals.length > 0) {
          for (const meal of meals) {
            try {
              const mealId = getMealIdForBackend(meal);

              console.log('Saving meal to backend:', {
                mealId: mealId,
                mealName: meal.name,
                mealType: mealType,
                date: currentDate
              });

              // Send the meal ID to the backend (should be valid ObjectId)
              await mealPlansAPI.createOrUpdateMealPlan({
                date: currentDate,
                mealType: mealType,
                meal: mealId  // Send the meal ID
              });

              // Track meal addition to meal plans for history
              try {
                await userAPI.addRecentlyAddedToMealPlan({
                  meal: meal,
                  addedToDate: currentDate,
                  addedToMealType: mealType
                });
                console.log(`Tracked ${meal.name} added to ${mealType} for ${currentDate}`);
              } catch (trackingError) {
                console.error('Error tracking meal addition:', trackingError);
                // Don't fail the main operation if tracking fails
              }
            } catch (error) {
              console.error(`Error saving ${mealType} meal:`, error);
            }
          }
        }
      }

      Alert.alert(
        'Success',
        'Meal plan updated successfully!',
        [
          {
            text: 'OK',
            onPress: () => navigation.goBack()
          }
        ]
      );
    } catch (error) {
      console.error('Error saving meal plan:', error);
      console.error('Error details:', error.response?.data);
      Alert.alert('Error', `Failed to save meal plan: ${error.response?.data?.message || error.message}`);
    } finally {
      setLoading(false);
    }
  };

  const renderMealTypeSelector = () => (
    <View style={styles.mealTypeSelector}>
      <Text style={styles.sectionTitle}>Select Meal Type</Text>
      <View style={styles.mealTypeButtons}>
        {mealTypes.map((type) => (
          <TouchableOpacity
            key={type}
            style={[
              styles.mealTypeButton,
              currentMealType === type && styles.mealTypeButtonActive
            ]}
            onPress={() => setCurrentMealType(type)}
          >
            <Text style={[
              styles.mealTypeButtonText,
              currentMealType === type && styles.mealTypeButtonTextActive
            ]}>
              {type.charAt(0).toUpperCase() + type.slice(1)}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );

  const renderSelectedMeals = () => {
    const meals = selectedMeals[currentMealType] || [];

    return (
      <View style={styles.selectedMealsSection}>
        <View style={styles.sectionHeader}>
          <Text style={styles.sectionTitle}>
            Selected {currentMealType.charAt(0).toUpperCase() + currentMealType.slice(1)} Meals
          </Text>
          <TouchableOpacity
            style={styles.addMealButton}
            onPress={() => setShowMealSelector(true)}
          >
            <Ionicons name="add-circle-outline" size={24} color={colors.primary} />
            <Text style={styles.addMealText}>Add Meal</Text>
          </TouchableOpacity>
        </View>

        {meals.length > 0 ? (
          <View style={styles.mealsListContainer}>
            {meals.map((meal, index) => (
              <View key={(meal.id || meal._id || index).toString()} style={styles.selectedMealCard}>
                <Image
                  source={{ uri: meal.image || 'https://via.placeholder.com/100x75' }}
                  style={styles.selectedMealImage}
                  resizeMode="cover"
                />
                <View style={styles.selectedMealInfo}>
                  <Text style={styles.selectedMealName} numberOfLines={1}>
                    {meal.name}
                  </Text>

                  {/* Description */}
                  {meal.description && (
                    <Text style={styles.selectedMealDescription} numberOfLines={1}>
                      {meal.description}
                    </Text>
                  )}

                  {/* Nutrition Summary */}
                  <View style={styles.selectedNutritionSummary}>
                    <Text style={styles.selectedNutritionText}>
                      {meal.calories || 0} cal
                    </Text>
                    {meal.protein && (
                      <Text style={styles.selectedNutritionText}>
                        • {meal.protein}g protein
                      </Text>
                    )}
                    {meal.carbs && (
                      <Text style={styles.selectedNutritionText}>
                        • {meal.carbs}g carbs
                      </Text>
                    )}
                  </View>

                  {/* Category and Rating */}
                  <View style={styles.selectedMealMeta}>
                    {meal.category && (
                      <View style={styles.selectedCategoryTag}>
                        <Text style={styles.selectedCategoryText}>
                          {Array.isArray(meal.category) ? meal.category[0] : meal.category}
                        </Text>
                      </View>
                    )}
                    {meal.rating && (
                      <View style={styles.selectedRatingContainer}>
                        <Ionicons name="star" size={12} color="#FFD700" />
                        <Text style={styles.selectedRatingText}>{meal.rating}</Text>
                      </View>
                    )}
                  </View>
                </View>
                <TouchableOpacity
                  style={styles.removeMealButton}
                  onPress={() => handleMealSelect(meal)}
                >
                  <Ionicons name="close-circle" size={24} color={colors.secondary} />
                </TouchableOpacity>
              </View>
            ))}
          </View>
        ) : (
          <View style={styles.emptyMealsContainer}>
            <Ionicons name="restaurant-outline" size={48} color={colors.textSecondary} />
            <Text style={styles.emptyMealsText}>
              No {currentMealType} meals selected
            </Text>
            <Text style={styles.emptyMealsSubtext}>
              Tap "Add Meal" to choose from Filipino dishes
            </Text>
          </View>
        )}
      </View>
    );
  };

  const renderMealSelectorModal = () => (
    <Modal
      visible={showMealSelector}
      animationType="slide"
      presentationStyle="pageSheet"
    >
      <SafeAreaView style={styles.modalContainer}>
        {/* Modal Header */}
        <View style={styles.modalHeader}>
          <TouchableOpacity onPress={() => setShowMealSelector(false)}>
            <Text style={styles.modalCancelText}>Cancel</Text>
          </TouchableOpacity>
          <Text style={styles.modalTitle}>
            Select {currentMealType.charAt(0).toUpperCase() + currentMealType.slice(1)} Meals
          </Text>
          <TouchableOpacity onPress={() => setShowMealSelector(false)}>
            <Text style={styles.modalDoneText}>Done</Text>
          </TouchableOpacity>
        </View>

        {/* Search Bar */}
        <View style={styles.searchContainer}>
          <View style={styles.searchInputContainer}>
            <Ionicons name="search" size={20} color={colors.textSecondary} />
            <TextInput
              style={styles.searchInput}
              placeholder="Search Filipino dishes..."
              value={searchQuery}
              onChangeText={setSearchQuery}
              placeholderTextColor={colors.textSecondary}
            />
            {searchQuery.length > 0 && (
              <TouchableOpacity onPress={() => setSearchQuery('')}>
                <Ionicons name="close-circle" size={20} color={colors.textSecondary} />
              </TouchableOpacity>
            )}
          </View>
        </View>

        {/* Meals List */}
        <FlatList
          data={filteredMeals}
          renderItem={({ item: meal }) => {
            const isSelected = (selectedMeals[currentMealType] || [])
              .some(m => (m.id || m._id) === (meal.id || meal._id));

            return (
              <TouchableOpacity
                style={[styles.mealSelectorCard, isSelected && styles.mealSelectorCardSelected]}
                onPress={() => handleMealSelect(meal)}
              >
                <Image
                  source={{ uri: meal.image || 'https://via.placeholder.com/120x90' }}
                  style={styles.mealSelectorImage}
                  resizeMode="cover"
                />
                <View style={styles.mealSelectorInfo}>
                  <Text style={styles.mealSelectorName} numberOfLines={2}>
                    {meal.name}
                  </Text>

                  {/* Description */}
                  {meal.description && (
                    <Text style={styles.mealDescription} numberOfLines={2}>
                      {meal.description}
                    </Text>
                  )}

                  {/* Nutrition Info */}
                  <View style={styles.nutritionInfo}>
                    <View style={styles.nutritionItem}>
                      <Text style={styles.nutritionLabel}>Calories</Text>
                      <Text style={styles.nutritionValue}>{meal.calories || 0}</Text>
                    </View>
                    {meal.protein && (
                      <View style={styles.nutritionItem}>
                        <Text style={styles.nutritionLabel}>Protein</Text>
                        <Text style={styles.nutritionValue}>{meal.protein}g</Text>
                      </View>
                    )}
                    {meal.carbs && (
                      <View style={styles.nutritionItem}>
                        <Text style={styles.nutritionLabel}>Carbs</Text>
                        <Text style={styles.nutritionValue}>{meal.carbs}g</Text>
                      </View>
                    )}
                    {meal.fat && (
                      <View style={styles.nutritionItem}>
                        <Text style={styles.nutritionLabel}>Fat</Text>
                        <Text style={styles.nutritionValue}>{meal.fat}g</Text>
                      </View>
                    )}
                  </View>

                  {/* Meta Info */}
                  <View style={styles.mealSelectorMeta}>
                    {meal.category && (
                      <View style={styles.categoryTag}>
                        <Text style={styles.categoryTagText}>
                          {Array.isArray(meal.category) ? meal.category[0] : meal.category}
                        </Text>
                      </View>
                    )}
                    {meal.rating && (
                      <View style={styles.ratingContainer}>
                        <Ionicons name="star" size={14} color="#FFD700" />
                        <Text style={styles.ratingText}>{meal.rating}</Text>
                      </View>
                    )}
                    {meal.prepTime && (
                      <View style={styles.timeContainer}>
                        <Ionicons name="time-outline" size={14} color={colors.textSecondary} />
                        <Text style={styles.timeText}>{meal.prepTime}min</Text>
                      </View>
                    )}
                  </View>

                  {/* Dietary Tags */}
                  {meal.dietaryTags && meal.dietaryTags.length > 0 && (
                    <View style={styles.dietaryTags}>
                      {meal.dietaryTags.slice(0, 3).map((tag, index) => (
                        <View key={index} style={styles.dietaryTag}>
                          <Text style={styles.dietaryTagText}>{tag}</Text>
                        </View>
                      ))}
                    </View>
                  )}
                </View>
                <View style={styles.mealSelectorAction}>
                  <Ionicons
                    name={isSelected ? "checkmark-circle" : "add-circle-outline"}
                    size={28}
                    color={isSelected ? colors.primary : colors.textSecondary}
                  />
                </View>
              </TouchableOpacity>
            );
          }}
          keyExtractor={(item, index) => (item.id || item._id || index).toString()}
          contentContainerStyle={styles.mealsList}
          ListEmptyComponent={
            <View style={commonStyles.emptyContainer}>
              <Ionicons name="restaurant-outline" size={64} color={colors.textSecondary} />
              <Text style={commonStyles.emptyTitle}>No meals found</Text>
              <Text style={commonStyles.emptySubtitle}>
                Try adjusting your search terms
              </Text>
            </View>
          }
        />
      </SafeAreaView>
    </Modal>
  );

  return (
    <SafeAreaView style={commonStyles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <Ionicons name="arrow-back" size={24} color={colors.surface} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Create Meal Plan</Text>
        <TouchableOpacity onPress={handleSaveMealPlan} disabled={loading}>
          <Text style={[styles.saveButton, loading && styles.saveButtonDisabled]}>
            {loading ? 'Saving...' : 'Save'}
          </Text>
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.container}>
        {/* Date Display */}
        <View style={styles.dateSection}>
          <Text style={styles.dateLabel}>Planning for:</Text>
          <Text style={styles.dateText}>
            {new Date(currentDate).toLocaleDateString('en-US', {
              weekday: 'long',
              year: 'numeric',
              month: 'long',
              day: 'numeric',
            })}
          </Text>
        </View>

        {/* Meal Type Selector */}
        {renderMealTypeSelector()}

        {/* Selected Meals */}
        {renderSelectedMeals()}

        {/* Summary */}
        <View style={styles.summarySection}>
          <Text style={styles.sectionTitle}>Meal Plan Summary</Text>
          {mealTypes.map(type => {
            const meals = selectedMeals[type] || [];
            const totalCalories = meals.reduce((sum, meal) => sum + (meal.calories || 0), 0);

            return (
              <View key={type} style={styles.summaryRow}>
                <Text style={styles.summaryMealType}>
                  {type.charAt(0).toUpperCase() + type.slice(1)}:
                </Text>
                <Text style={styles.summaryCount}>
                  {meals.length} meal{meals.length !== 1 ? 's' : ''} ({totalCalories} cal)
                </Text>
              </View>
            );
          })}
        </View>
      </ScrollView>

      {/* Meal Selector Modal */}
      {renderMealSelectorModal()}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    backgroundColor: colors.primary,
    paddingVertical: spacing.md,
    paddingHorizontal: spacing.md,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  headerTitle: {
    fontSize: fonts.sizes.large,
    fontWeight: 'bold',
    color: colors.surface,
  },
  saveButton: {
    fontSize: fonts.sizes.medium,
    fontWeight: 'bold',
    color: colors.surface,
  },
  saveButtonDisabled: {
    opacity: 0.6,
  },
  dateSection: {
    backgroundColor: colors.surface,
    padding: spacing.md,
    alignItems: 'center',
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  dateLabel: {
    fontSize: fonts.sizes.medium,
    color: colors.textSecondary,
    marginBottom: spacing.xs,
  },
  dateText: {
    fontSize: fonts.sizes.large,
    fontWeight: 'bold',
    color: colors.text,
    textAlign: 'center',
  },
  mealTypeSelector: {
    backgroundColor: colors.surface,
    padding: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  sectionTitle: {
    fontSize: fonts.sizes.medium,
    fontWeight: 'bold',
    color: colors.text,
    marginBottom: spacing.md,
  },
  mealTypeButtons: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: spacing.sm,
  },
  mealTypeButton: {
    backgroundColor: colors.background,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderRadius: borderRadius.medium,
    borderWidth: 1,
    borderColor: colors.border,
  },
  mealTypeButtonActive: {
    backgroundColor: colors.primary,
    borderColor: colors.primary,
  },
  mealTypeButtonText: {
    fontSize: fonts.sizes.medium,
    color: colors.text,
    fontWeight: '500',
  },
  mealTypeButtonTextActive: {
    color: colors.surface,
  },
  selectedMealsSection: {
    backgroundColor: colors.surface,
    padding: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  addMealButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.background,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderRadius: borderRadius.medium,
    borderWidth: 1,
    borderColor: colors.primary,
  },
  addMealText: {
    fontSize: fonts.sizes.small,
    color: colors.primary,
    marginLeft: spacing.xs,
    fontWeight: '500',
  },
  mealsListContainer: {
    maxHeight: 200,
  },
  selectedMealCard: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    backgroundColor: colors.surface,
    borderRadius: borderRadius.medium,
    padding: spacing.md,
    marginBottom: spacing.sm,
    borderWidth: 1,
    borderColor: colors.border,
    ...commonStyles.shadowSmall,
  },
  selectedMealImage: {
    width: 80,
    height: 60,
    borderRadius: borderRadius.small,
  },
  selectedMealInfo: {
    flex: 1,
    marginLeft: spacing.md,
  },
  selectedMealName: {
    fontSize: fonts.sizes.medium,
    fontWeight: '600',
    color: colors.text,
    marginBottom: spacing.xs,
  },
  selectedMealDescription: {
    fontSize: fonts.sizes.small,
    color: colors.textSecondary,
    marginBottom: spacing.xs,
    lineHeight: 16,
  },
  selectedNutritionSummary: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: spacing.xs,
  },
  selectedNutritionText: {
    fontSize: fonts.sizes.small,
    color: colors.textSecondary,
    marginRight: spacing.sm,
  },
  selectedMealMeta: {
    flexDirection: 'row',
    alignItems: 'center',
    flexWrap: 'wrap',
  },
  selectedCategoryTag: {
    backgroundColor: colors.primary,
    paddingHorizontal: spacing.sm,
    paddingVertical: 2,
    borderRadius: borderRadius.small,
    marginRight: spacing.sm,
  },
  selectedCategoryText: {
    color: colors.surface,
    fontSize: fonts.sizes.tiny || 10,
    fontWeight: '500',
  },
  selectedRatingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  selectedRatingText: {
    fontSize: fonts.sizes.small,
    color: colors.textSecondary,
    marginLeft: 2,
  },
  removeMealButton: {
    padding: spacing.sm,
  },
  emptyMealsContainer: {
    alignItems: 'center',
    paddingVertical: spacing.xl,
  },
  emptyMealsText: {
    fontSize: fonts.sizes.medium,
    fontWeight: '500',
    color: colors.textSecondary,
    marginTop: spacing.md,
  },
  emptyMealsSubtext: {
    fontSize: fonts.sizes.small,
    color: colors.textSecondary,
    marginTop: spacing.xs,
    textAlign: 'center',
  },
  summarySection: {
    backgroundColor: colors.surface,
    padding: spacing.md,
    marginTop: spacing.md,
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: spacing.sm,
  },
  summaryMealType: {
    fontSize: fonts.sizes.medium,
    fontWeight: '500',
    color: colors.text,
  },
  summaryCount: {
    fontSize: fonts.sizes.medium,
    color: colors.textSecondary,
  },
  modalContainer: {
    flex: 1,
    backgroundColor: colors.background,
  },
  modalHeader: {
    backgroundColor: colors.surface,
    paddingVertical: spacing.md,
    paddingHorizontal: spacing.md,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  modalTitle: {
    fontSize: fonts.sizes.large,
    fontWeight: 'bold',
    color: colors.text,
  },
  modalCancelText: {
    fontSize: fonts.sizes.medium,
    color: colors.textSecondary,
  },
  modalDoneText: {
    fontSize: fonts.sizes.medium,
    color: colors.primary,
    fontWeight: 'bold',
  },
  searchContainer: {
    backgroundColor: colors.surface,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.md,
  },
  searchInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.background,
    borderRadius: borderRadius.medium,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
  },
  searchInput: {
    flex: 1,
    fontSize: fonts.sizes.medium,
    color: colors.text,
    marginLeft: spacing.sm,
  },
  mealsList: {
    padding: spacing.md,
  },
  mealSelectorCard: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    backgroundColor: colors.surface,
    borderRadius: borderRadius.medium,
    padding: spacing.md,
    marginBottom: spacing.md,
    borderWidth: 1,
    borderColor: colors.border,
    ...commonStyles.shadowSmall,
  },
  mealSelectorCardSelected: {
    borderColor: colors.primary,
    backgroundColor: colors.background,
    borderWidth: 2,
  },
  mealSelectorImage: {
    width: 100,
    height: 75,
    borderRadius: borderRadius.small,
  },
  mealSelectorInfo: {
    flex: 1,
    marginLeft: spacing.md,
  },
  mealSelectorName: {
    fontSize: fonts.sizes.medium,
    fontWeight: '600',
    color: colors.text,
    marginBottom: spacing.xs,
  },
  mealDescription: {
    fontSize: fonts.sizes.small,
    color: colors.textSecondary,
    marginBottom: spacing.sm,
    lineHeight: 18,
  },
  nutritionInfo: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: spacing.sm,
  },
  nutritionItem: {
    marginRight: spacing.md,
    marginBottom: spacing.xs,
  },
  nutritionLabel: {
    fontSize: fonts.sizes.tiny || 10,
    color: colors.textSecondary,
    textTransform: 'uppercase',
    fontWeight: '500',
  },
  nutritionValue: {
    fontSize: fonts.sizes.small,
    color: colors.text,
    fontWeight: '600',
  },
  mealSelectorMeta: {
    flexDirection: 'row',
    alignItems: 'center',
    flexWrap: 'wrap',
    marginBottom: spacing.xs,
  },
  categoryTag: {
    backgroundColor: colors.primary,
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    borderRadius: borderRadius.small,
    marginRight: spacing.sm,
    marginBottom: spacing.xs,
  },
  categoryTagText: {
    color: colors.surface,
    fontSize: fonts.sizes.small,
    fontWeight: '500',
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: spacing.sm,
    marginBottom: spacing.xs,
  },
  ratingText: {
    fontSize: fonts.sizes.small,
    color: colors.textSecondary,
    marginLeft: 2,
  },
  timeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.xs,
  },
  timeText: {
    fontSize: fonts.sizes.small,
    color: colors.textSecondary,
    marginLeft: 2,
  },
  dietaryTags: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  dietaryTag: {
    backgroundColor: colors.background,
    borderWidth: 1,
    borderColor: colors.border,
    paddingHorizontal: spacing.sm,
    paddingVertical: 2,
    borderRadius: borderRadius.small,
    marginRight: spacing.xs,
    marginBottom: spacing.xs,
  },
  dietaryTagText: {
    fontSize: fonts.sizes.tiny || 10,
    color: colors.textSecondary,
    fontWeight: '500',
  },
  mealSelectorAction: {
    padding: spacing.sm,
    alignItems: 'center',
    justifyContent: 'center',
  },
});

export default CreateMealPlanScreen;
