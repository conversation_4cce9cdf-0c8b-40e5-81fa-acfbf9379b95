import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  TextInput,
  TouchableOpacity,
  FlatList,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { colors, fonts, spacing, borderRadius } from '../../styles/theme';
import { commonStyles } from '../../styles/commonStyles';

const ChatScreen = ({ navigation }) => {
  const [messages, setMessages] = useState([
    {
      id: '1',
      text: 'Hello! I\'m your Filipino cuisine assistant. I can help you with recipes, meal planning, and cooking tips. What would you like to know?',
      isBot: true,
      timestamp: new Date(),
    }
  ]);
  const [inputText, setInputText] = useState('');
  const [isTyping, setIsTyping] = useState(false);

  const sendMessage = async () => {
    if (!inputText.trim()) return;

    const userMessage = {
      id: Date.now().toString(),
      text: inputText.trim(),
      isBot: false,
      timestamp: new Date(),
    };

    setMessages(prev => [...prev, userMessage]);
    setInputText('');
    setIsTyping(true);

    // Simulate bot response
    setTimeout(() => {
      const botResponse = generateBotResponse(userMessage.text);
      const botMessage = {
        id: (Date.now() + 1).toString(),
        text: botResponse,
        isBot: true,
        timestamp: new Date(),
      };
      
      setMessages(prev => [...prev, botMessage]);
      setIsTyping(false);
    }, 1500);
  };

  const generateBotResponse = (userText) => {
    const lowerText = userText.toLowerCase();
    
    if (lowerText.includes('adobo')) {
      return 'Adobo is a classic Filipino dish! It\'s made with meat (usually pork or chicken) marinated in vinegar, soy sauce, garlic, and spices. Would you like me to help you find an adobo recipe or add it to your meal plan?';
    } else if (lowerText.includes('recipe') || lowerText.includes('cook')) {
      return 'I can help you find amazing Filipino recipes! Some popular dishes include Adobo, Sinigang, Lechon Kawali, and Pancit. What type of dish are you in the mood for?';
    } else if (lowerText.includes('meal plan') || lowerText.includes('plan')) {
      return 'Great! I can help you create a meal plan with delicious Filipino dishes. Would you like me to suggest meals for this week based on your preferences?';
    } else if (lowerText.includes('ingredient')) {
      return 'Filipino cuisine uses many unique ingredients like fish sauce (patis), shrimp paste (bagoong), coconut milk, and various tropical vegetables. What specific ingredient would you like to know about?';
    } else if (lowerText.includes('hello') || lowerText.includes('hi')) {
      return 'Hello! I\'m excited to help you explore Filipino cuisine. Are you looking for recipe suggestions, cooking tips, or help with meal planning?';
    } else {
      return 'That\'s interesting! I\'m here to help with Filipino recipes, cooking techniques, and meal planning. Feel free to ask me about any Filipino dish, ingredient, or cooking method you\'d like to learn about!';
    }
  };

  const renderMessage = ({ item: message }) => (
    <View style={[
      styles.messageContainer,
      message.isBot ? styles.botMessage : styles.userMessage
    ]}>
      <View style={[
        styles.messageBubble,
        message.isBot ? styles.botBubble : styles.userBubble
      ]}>
        <Text style={[
          styles.messageText,
          message.isBot ? styles.botText : styles.userText
        ]}>
          {message.text}
        </Text>
        <Text style={[
          styles.timestamp,
          message.isBot ? styles.botTimestamp : styles.userTimestamp
        ]}>
          {message.timestamp.toLocaleTimeString([], { 
            hour: '2-digit', 
            minute: '2-digit' 
          })}
        </Text>
      </View>
    </View>
  );

  const renderTypingIndicator = () => (
    <View style={[styles.messageContainer, styles.botMessage]}>
      <View style={[styles.messageBubble, styles.botBubble]}>
        <View style={styles.typingIndicator}>
          <View style={[styles.typingDot, { animationDelay: '0ms' }]} />
          <View style={[styles.typingDot, { animationDelay: '150ms' }]} />
          <View style={[styles.typingDot, { animationDelay: '300ms' }]} />
        </View>
      </View>
    </View>
  );

  const quickSuggestions = [
    'Show me adobo recipes',
    'Plan meals for this week',
    'What ingredients do I need for sinigang?',
    'Suggest breakfast dishes',
  ];

  const renderQuickSuggestion = (suggestion) => (
    <TouchableOpacity
      key={suggestion}
      style={styles.suggestionButton}
      onPress={() => {
        setInputText(suggestion);
        sendMessage();
      }}
    >
      <Text style={styles.suggestionText}>{suggestion}</Text>
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={commonStyles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity 
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="arrow-back" size={24} color={colors.surface} />
        </TouchableOpacity>
        <View style={styles.headerInfo}>
          <Text style={styles.headerTitle}>Filipino Cuisine Assistant</Text>
          <Text style={styles.headerSubtitle}>Always here to help</Text>
        </View>
        <View style={styles.headerRight}>
          <View style={styles.onlineIndicator} />
        </View>
      </View>

      <KeyboardAvoidingView 
        style={styles.container}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        {/* Messages */}
        <FlatList
          data={messages}
          renderItem={renderMessage}
          keyExtractor={(item) => item.id}
          style={styles.messagesList}
          contentContainerStyle={styles.messagesContainer}
          ListFooterComponent={isTyping ? renderTypingIndicator : null}
        />

        {/* Quick Suggestions */}
        {messages.length === 1 && (
          <View style={styles.suggestionsContainer}>
            <Text style={styles.suggestionsTitle}>Quick suggestions:</Text>
            <View style={styles.suggestionsGrid}>
              {quickSuggestions.map(renderQuickSuggestion)}
            </View>
          </View>
        )}

        {/* Input */}
        <View style={styles.inputContainer}>
          <View style={styles.inputWrapper}>
            <TextInput
              style={styles.textInput}
              value={inputText}
              onChangeText={setInputText}
              placeholder="Ask about Filipino recipes, cooking tips..."
              placeholderTextColor={colors.textSecondary}
              multiline
              maxLength={500}
            />
            <TouchableOpacity
              style={[
                styles.sendButton,
                inputText.trim() ? styles.sendButtonActive : styles.sendButtonInactive
              ]}
              onPress={sendMessage}
              disabled={!inputText.trim()}
            >
              <Ionicons 
                name="send" 
                size={20} 
                color={inputText.trim() ? colors.surface : colors.textSecondary} 
              />
            </TouchableOpacity>
          </View>
        </View>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    backgroundColor: colors.primary,
    paddingVertical: spacing.md,
    paddingHorizontal: spacing.md,
    flexDirection: 'row',
    alignItems: 'center',
  },
  backButton: {
    padding: spacing.sm,
    marginRight: spacing.sm,
  },
  headerInfo: {
    flex: 1,
  },
  headerTitle: {
    fontSize: fonts.sizes.large,
    fontWeight: 'bold',
    color: colors.surface,
  },
  headerSubtitle: {
    fontSize: fonts.sizes.small,
    color: colors.surface,
    opacity: 0.8,
  },
  headerRight: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  onlineIndicator: {
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: '#4CAF50',
  },
  messagesList: {
    flex: 1,
  },
  messagesContainer: {
    padding: spacing.md,
    paddingBottom: spacing.lg,
  },
  messageContainer: {
    marginBottom: spacing.md,
  },
  botMessage: {
    alignItems: 'flex-start',
  },
  userMessage: {
    alignItems: 'flex-end',
  },
  messageBubble: {
    maxWidth: '80%',
    borderRadius: borderRadius.large,
    padding: spacing.md,
  },
  botBubble: {
    backgroundColor: colors.surface,
    borderBottomLeftRadius: borderRadius.small,
  },
  userBubble: {
    backgroundColor: colors.primary,
    borderBottomRightRadius: borderRadius.small,
  },
  messageText: {
    fontSize: fonts.sizes.medium,
    lineHeight: 20,
  },
  botText: {
    color: colors.text,
  },
  userText: {
    color: colors.surface,
  },
  timestamp: {
    fontSize: fonts.sizes.small,
    marginTop: spacing.xs,
  },
  botTimestamp: {
    color: colors.textSecondary,
  },
  userTimestamp: {
    color: colors.surface,
    opacity: 0.8,
  },
  typingIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  typingDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: colors.textSecondary,
    marginHorizontal: 2,
  },
  suggestionsContainer: {
    padding: spacing.md,
    backgroundColor: colors.background,
    borderTopWidth: 1,
    borderTopColor: colors.border,
  },
  suggestionsTitle: {
    fontSize: fonts.sizes.medium,
    fontWeight: '500',
    color: colors.text,
    marginBottom: spacing.md,
  },
  suggestionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: spacing.sm,
  },
  suggestionButton: {
    backgroundColor: colors.surface,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderRadius: borderRadius.medium,
    borderWidth: 1,
    borderColor: colors.border,
  },
  suggestionText: {
    fontSize: fonts.sizes.small,
    color: colors.primary,
    fontWeight: '500',
  },
  inputContainer: {
    backgroundColor: colors.surface,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.md,
    borderTopWidth: 1,
    borderTopColor: colors.border,
  },
  inputWrapper: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    backgroundColor: colors.background,
    borderRadius: borderRadius.large,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    minHeight: 44,
  },
  textInput: {
    flex: 1,
    fontSize: fonts.sizes.medium,
    color: colors.text,
    maxHeight: 100,
    paddingVertical: spacing.sm,
  },
  sendButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    alignItems: 'center',
    justifyContent: 'center',
    marginLeft: spacing.sm,
  },
  sendButtonActive: {
    backgroundColor: colors.primary,
  },
  sendButtonInactive: {
    backgroundColor: colors.border,
  },
});

export default ChatScreen;
